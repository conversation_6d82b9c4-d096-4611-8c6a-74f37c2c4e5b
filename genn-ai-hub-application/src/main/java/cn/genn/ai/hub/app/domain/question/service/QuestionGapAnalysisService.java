package cn.genn.ai.hub.app.domain.question.service;

import cn.genn.ai.hub.app.application.assembler.GeneralAssembler;
import cn.genn.ai.hub.app.application.command.GapAnalysisRequest;
import cn.genn.ai.hub.app.application.command.GapAnalysisTaskConfigRequest;
import cn.genn.ai.hub.app.application.command.QuestionGapAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.question.GapAnalysisInfo;
import cn.genn.ai.hub.app.application.dto.question.MissingPoints;
import cn.genn.ai.hub.app.application.dto.question.QuestionGapAnalysisDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionInfoDTO;
import cn.genn.ai.hub.app.application.dto.request.RequestWebModel;
import cn.genn.ai.hub.app.application.query.QuestionGapAnalysisQuery;
import cn.genn.ai.hub.app.infrastructure.config.AgentProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.constant.AgentAppConstants;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.QuestionGapAnalysisRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.QuestionRepositoryImpl;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.job.xxl.model.task.*;
import cn.genn.job.xxl.service.ScheduledTaskService;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QuestionGapAnalysisService {

    private final QuestionRepositoryImpl questionRepository;
    private final QuestionGapAnalysisRepositoryImpl gapAnalysisRepository;
    private final GeneralAssembler generalAssembler;
    private final ScheduledTaskService scheduledTaskService;
    private final GennAIHubProperties gennAIHubProperties;

    public List<GapAnalysisInfo> gapList(QuestionGapAnalysisQuery query) {
        if (query.getSources() != null && query.getSources().isEmpty()) {
            return Lists.newArrayList();
        }
        return Optional.ofNullable(gapAnalysisRepository.infos(query))
            .orElse(Collections.emptyList())
            .stream()
            .collect(Collectors.groupingBy(
                QuestionGapAnalysisDTO::getType,
                Collectors.mapping(QuestionGapAnalysisDTO::getQuestionId, Collectors.toSet())
            ))
            .entrySet()
            .stream()
            .map(entry ->
                GapAnalysisInfo
                    .builder()
                    .type(entry.getKey())
                    .questionIds(Lists.newArrayList(entry.getValue()))
                    .build())
            .sorted(Comparator.comparingInt(info -> -info.getQuestionIds().size())) // 降序
            .collect(Collectors.toList());
    }

    public Map<String, List<QuestionGapAnalysisDTO>> singleMap(QuestionGapAnalysisQuery query) {
        return Optional.ofNullable(gapAnalysisRepository.infos(query))
            .orElse(Collections.emptyList())
            .stream()
            .filter(gap -> {
                // 排除类型为"公共知识"的记录
                if (StringUtils.equals(gap.getType(), "公共知识")) {
                    return false;
                }
                // 过滤掉缺失点中包含"公共知识"原因的项
                List<MissingPoints> filteredPoints = Optional.ofNullable(gap.getMissingPoints())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(point -> !point.getReason().contains("公共知识"))
                    .collect(Collectors.toList());

                // 如果过滤后没有剩余项，则排除该记录
                if (filteredPoints.isEmpty()) {
                    return false;
                }
                // 更新gap中的missingPoints为过滤后的结果
                gap.setMissingPoints(filteredPoints);
                return true;
            })
            .collect(Collectors.groupingBy(
                QuestionGapAnalysisDTO::getChatId));
    }

    @Transactional
    public void add(GapAnalysisRequest result) {
        QuestionInfoDTO infoDTO = questionRepository.queryById(result.getQuestionId());
        QuestionGapAnalysisCommand command = generalAssembler.convertGapAnl(infoDTO);
        GapAnalysisRequest.GapAnl gapAnl = JsonUtils.parse(result.getResult(), GapAnalysisRequest.GapAnl.class);
        if (CollUtil.isEmpty(gapAnl.getMissingPoints())) {
            return;
        }
        command.setSummary(gapAnl.getSummary());

        Map<String, List<MissingPoints>> typeGroups = gapAnl.getMissingPoints().stream().collect(Collectors.groupingBy(MissingPoints::getType));
        typeGroups.forEach((type, points) -> {
            command.setType(type);
            command.setMissingPoints(JsonUtils.toJson(points));
            gapAnalysisRepository.add(command);
        });
    }

    public JobScheduledTasksDTO query(TaskQuery query) {
        List<JobScheduledTasksDTO> taskList = scheduledTaskService.query(query);
        if (CollUtil.isNotEmpty(taskList)) {
            return taskList.getFirst();
        }
        return null;
    }

    public void taskConfig(GapAnalysisTaskConfigRequest request) {
        JobTaskContext context = new JobTaskContext();
        context.setClassName(this.getClass().getName());
        context.setMethodName("gapAnalysisCallback");
        Map<String, Object> params = new HashMap<>();
        params.put("appId", request.getAppId());
        params.put("minusDays", request.getMinusDays());
        context.setParams(params);
        context.setTaskType(JobTaskTypeEnum.LOCAL);

        TaskSaveOrUpdateCommand taskCommand = new TaskSaveOrUpdateCommand();
        taskCommand.setId(request.getId());
        taskCommand.setName("知识缺失分析");
        taskCommand.setBizKey(request.getAppId());
        taskCommand.setPlan(request.getPlan());
        taskCommand.setStatus(request.getStatus());
        taskCommand.setContent(context);
        taskCommand.setPriority(0);
        scheduledTaskService.saveOrUpdate(taskCommand);
    }

    /**
     * 定时调度回调入口 - 执行知识缺失分析任务
     *
     * @param appId     应用ID（工作流ID）
     * @param minusDays 要扫描的天数范围（往前推多少天）
     * @return 执行结果信息
     */
    public String gapAnalysisCallback(String appId, Integer minusDays) {
        LocalDate right = LocalDate.now();
        LocalDate left = right.minusDays(minusDays);

        // 获取指定时间范围内的问题数据
        List<QuestionInfoDTO> infoDTOS = questionRepository.getQuestionByAppId(appId, left, right);
        if (CollUtil.isEmpty(infoDTOS)) {
            log.info("没有需要分析的数据，appId: {}, 时间范围: {} ~ {}", appId, left, right);
            return "没有需要分析的数据";
        }

        // 筛选出尚未分析过的问题
        List<Long> qIds = infoDTOS.stream().map(QuestionInfoDTO::getId).toList();
        List<QuestionGapAnalysisDTO> existingAnalyses = gapAnalysisRepository.queryByQuestionIds(qIds);
        Set<Long> analyzedQuestionIds = existingAnalyses.stream()
            .map(QuestionGapAnalysisDTO::getQuestionId)
            .collect(Collectors.toSet());

        List<QuestionInfoDTO> needProcess = infoDTOS.stream()
            .filter(dto -> !analyzedQuestionIds.contains(dto.getId()))
            .toList();

        if (needProcess.isEmpty()) {
            log.info("所有问题已完成知识缺失分析，appId: {}", appId);
            return "无需再次分析，所有问题已处理完成";
        }

        // 构建请求并调用工作流服务
        AgentProperties agent = gennAIHubProperties.getAgent();
        String url = agent.getInvokeDomain() + AgentAppConstants.INVOKE_APP_URL;
        List<String> failedQuestions = new ArrayList<>();
        // 查询现有标签
        List<String> labels = gapAnalysisRepository.queryTypes(appId);
        for (QuestionInfoDTO infoDTO : needProcess) {
            try {
                RequestWebModel request = buildRequestModel(infoDTO, labels);
                String requestBody = JsonUtils.toJson(request);

                boolean success = invokeWorkflow(url, requestBody, agent.getGapAnlApiKey(), agent.getMaxRetries());
                if (!success) {
                    failedQuestions.add(infoDTO.getQuestion());
                }
            } catch (Exception e) {
                log.error("构建或发送请求失败，questionId: {}", infoDTO.getId(), e);
                failedQuestions.add(infoDTO.getQuestion());
            }
        }

        // 返回执行结果
        if (!failedQuestions.isEmpty()) {
            return String.format("工作流ID: %s, 扫描前%s日数据(包含当天), 新增分析数据个数: %s, 失败问题: %s",
                appId, minusDays, needProcess.size(), failedQuestions);
        }

        return String.format("工作流ID: %s, 扫描前%s日数据(包含当天), 新增分析数据个数: %s", appId, minusDays, needProcess.size());
    }

    /**
     * 构建请求模型对象
     */
    private RequestWebModel buildRequestModel(QuestionInfoDTO infoDTO, List<String> labels) {
        RequestWebModel request = new RequestWebModel();
        RequestWebModel.Message message = request.getMessages().getFirst();
        message.setContent(infoDTO.getQuestion());

        RequestWebModel.Variables variables = new RequestWebModel.Variables();
        variables.setQuestionId(infoDTO.getId());
        variables.setQuestion(infoDTO.getQuestion());
        variables.setAnswer(infoDTO.getAnswer());
        variables.setLabels(labels);
        request.setVariables(variables);
        return request;
    }

    public boolean invokeWorkflow(String url, String body, String apiKey, int maxRetries) {

        int retryCount = 0;
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(90000); // 连接超时时间（ms）
        factory.setReadTimeout(50000);   // 读取超时时间（ms）

        RestTemplate restTemplate = new RestTemplate(factory);
        while (retryCount < maxRetries) {
            try {

                HttpHeaders headers = new HttpHeaders();
                headers.add("Content-Type", "application/json; charset=utf-8");
                headers.add("Authorization", "Bearer " + apiKey);

                HttpEntity<String> httpEntity = new HttpEntity<>(body, headers);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                log.info("invoke, result status code: {}", responseEntity.getStatusCode());

                if (HttpStatus.OK != responseEntity.getStatusCode()) {
                    log.error("invoke, failed with status code: {}", responseEntity.getStatusCode());
                    retryCount++;
                    continue; // 继续重试
                }

                if (responseEntity.getBody().startsWith("{\"error\"")) {
                    retryCount++;
                    continue; // 继续重试
                }
                // 成功处理响应体
                break; // 成功跳出循环
            } catch (Exception e) {
                log.error("调用工作流出错: ", e);
                retryCount++;
                if (retryCount >= maxRetries) {
                    return false;
                }
            }
        }
        return true;
    }

}
