package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.command.QuestionAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.question.QuestionAnalysisDTO;
import cn.genn.ai.hub.app.application.dto.question.DepartmentQuestionCountDTO;
import cn.genn.ai.hub.app.application.dto.question.UserQuestionCountDTO;
import cn.genn.ai.hub.app.application.query.DepartmentQuestionCountQuery;
import cn.genn.ai.hub.app.application.query.QuestionAnalysisQuery;
import cn.genn.ai.hub.app.application.query.UserQuestionCountQuery;
import cn.genn.ai.hub.app.domain.question.service.QuestionAnalysisService;
import cn.genn.ai.hub.app.domain.question.service.QuestionInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "高频问题统计")
@RestController
@RequestMapping("/question/analysis")
@RequiredArgsConstructor
public class QuestionAnalysisController {

    private final QuestionAnalysisService questionAnalysisService;
    private final QuestionInfoService questionInfoService;


    /**
     * 查询列表
     *
     * @param query 查询条件
     * @return list
     */
    @PostMapping("/list")
    @Operation(summary = "查询列表")
    public List<QuestionAnalysisDTO> analysisDTOs(@Parameter(description = "查询类") @RequestBody QuestionAnalysisQuery query) {
        return questionAnalysisService.analysisDTOs(query);
    }


    /**
     * 添加记录
     *
     * @param commands commands
     * @return
     */
    @PostMapping("/batch/add")
    @Operation(summary = "新增记录")
    public void addBatch(@Parameter(description = "commands") @RequestBody List<QuestionAnalysisCommand> commands) {
        questionAnalysisService.addBatch(commands);
    }


    /**
     * 获取咨询用户top10（包含问题详情）
     *
     * @param query 查询条件
     * @return 用户统计列表
     */
    @PostMapping("/user/top")
    @Operation(summary = "获取咨询用户top10（包含问题详情）")
    public List<UserQuestionCountDTO> getTopUsersWithDetails(@Parameter(description = "查询条件") @RequestBody UserQuestionCountQuery query) {
        return questionInfoService.getTopUsersWithDetails(query);
    }

    /**
     * 获取咨询部门top10（包含人员详情）
     *
     * @param query 查询条件
     * @return 部门统计列表
     */
    @PostMapping("/department/top")
    @Operation(summary = "获取咨询部门top10（包含人员详情）")
    public List<DepartmentQuestionCountDTO> getDepartmentWithDetails(@Parameter(description = "查询条件") @RequestBody DepartmentQuestionCountQuery query) {
        return questionInfoService.getDepartmentWithDetails(query);
    }

}

