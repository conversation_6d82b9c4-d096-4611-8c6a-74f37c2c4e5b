package cn.genn.ai.hub.app.application.service.action;

import cn.genn.ai.hub.app.application.dto.*;
import cn.genn.ai.hub.app.application.dto.embedding.KbSearchCasesData;
import cn.genn.ai.hub.app.application.dto.embedding.KbSearchQaPairData;
import cn.genn.ai.hub.app.application.enums.CollectionTypeEnum;
import cn.genn.ai.hub.app.application.enums.DataTypeEnum;
import cn.genn.ai.hub.app.application.processor.AIHubInvokeService;
import cn.genn.ai.hub.app.application.service.query.KbRepoDataQueryService;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.MilvusProperties;
import cn.genn.ai.hub.app.infrastructure.config.RerankerProperties;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.CasesRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepCollectionRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.ai.hub.app.infrastructure.utils.vector.milvus.MilvusFluentFilterBuilder;
import cn.genn.ai.hub.app.infrastructure.utils.vector.milvus.MilvusUtils;
import cn.genn.ai.hub.core.api.kb.KbSearchRequest;
import cn.genn.ai.hub.core.api.kb.KbSearchResponse;
import cn.genn.ai.hub.core.api.kb.KbSearchResultItem;
import cn.genn.ai.hub.plugin.common.aop.LogMonitor;
import cn.genn.core.model.res.ResponseResult;
import cn.genn.core.utils.VirtualThreadUtils;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import cn.genn.spring.boot.starter.ai.properties.AIProviderProperties;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.model.SsoUserAuthInfoDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.reflect.TypeToken;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.ConsistencyLevel;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.LoadCollectionReq;
import io.milvus.v2.service.vector.request.AnnSearchReq;
import io.milvus.v2.service.vector.request.HybridSearchReq;
import io.milvus.v2.service.vector.request.SearchReq;
import io.milvus.v2.service.vector.request.data.BaseVector;
import io.milvus.v2.service.vector.request.data.EmbeddedText;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.request.ranker.BaseRanker;
import io.milvus.v2.service.vector.request.ranker.RRFRanker;
import io.milvus.v2.service.vector.request.ranker.WeightedRanker;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClient;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Date: 2025/3/18
 * @Author: kangjian
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KbRepoSearchActionService {

    private static final List<String> COMMON_OUTPUT_FIELDS =
        List.of("data_key", "content", "index_key");

    private static final List<String> COMMON_OUTPUT_FIELDS_QA =
        List.of("qa_pair_key", "question", "answer", "repo_id");

    private static final List<String> COMMON_OUTPUT_FIELDS_CASES =
        List.of("cases_id", "repo_id", "title", "content", "impact");

    protected final GennAIHubProperties properties;
    protected final AIHubInvokeService invokeService;
    private final KbRepoDataQueryService dataQueryService;
    private final KbRepoBaseInfoMapper kbRepoBaseInfoMapper;
    private final KbRepCollectionRepositoryImpl collectionRepository;
    private final MilvusClientV2 milvusClient;
    private final AIModelManager aiModelManager;
    private final FileStorageOperation fileStorageOperation;
    private final RestClient restClient;
    private final KbRepoFileActionService fileActionService;
    private final CasesRepositoryImpl repositoryImpl;

    @IgnoreTenant
    public KbSearchResponse searchOfSingleRepo(KbSearchRequest request) {
        // 查询知识库下所有未启用的数据集id集合
        KbRepoBaseInfoPO kbRepoBaseInfoPO = kbRepoBaseInfoMapper.selectById(request.getRepoIds().get(0));
        List<KbRepoCollectionDTO> collectionDTOS = collectionRepository.selectDisableCollectByRepoId(request.getRepoIds());
        KbRepoSearchBody searchBody = buildSearchBody(kbRepoBaseInfoPO, collectionDTOS, request);
        return invokeLocalSearch(searchBody);
    }

    public String realtimeFileParse(String externalFileId, String externalFileUrl, String fileName, String contentType) {
        // 构造对象调用算法
        KbRepoTaskOfFileParseBody realtimeFileParseBody = buildRealtimeFileParseBody(externalFileId, externalFileUrl, fileName, contentType);

        int maxRetries = 3; // 最大重试次数
        int retryInterval = 1000; // 重试间隔时间（毫秒）

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                String url = properties.getKbInvokeUrl().getRealtimeParseFileUrl();
                TypeToken<Map> typeToken = new TypeToken<>() {
                };
                ResponseResult<Map> result = invokeService.fetchPost(url, JsonUtils.toJson(realtimeFileParseBody), typeToken);
                if (result.isSuccess()) {
                    return String.valueOf(result.getData().getOrDefault("text", ""));
                } else {
                    log.error("Failed to fetch search data: {}", result.getMsg());
                    throw new RuntimeException(result.getMsg());
                }
            } catch (Exception e) {
                log.error("realtimeFileParse invoke error on attempt {}", attempt + 1, e);
                if (attempt < maxRetries - 1) {
                    try {
                        Thread.sleep(retryInterval);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("Thread interrupted", ie);
                    }
                } else {
                    log.error("Max retries reached. realtimeFileParse invoke error", e);
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public List<KbRepoRealtimeFileParseBody> realtimeFileParseChunk(String externalFileId, String externalFileUrl, String fileName, String contentType) {
        // 构造对象调用算法
        KbRepoTaskOfFileParseBody realtimeFileParseBody = buildRealtimeFileParseBody(externalFileId, externalFileUrl, fileName, contentType);

        int maxRetries = 3; // 最大重试次数
        int retryInterval = 1000; // 重试间隔时间（毫秒）

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                String url = properties.getKbInvokeUrl().getRealtimeParseFileChunkPath();
                TypeToken<List<KbRepoRealtimeFileParseBody>> typeToken = new TypeToken<List<KbRepoRealtimeFileParseBody>>() {
                };
                ResponseResult<List<KbRepoRealtimeFileParseBody>> result = invokeService.fetchPost(url, JsonUtils.toJson(realtimeFileParseBody), typeToken);
                if (result.isSuccess()) {
                    return result.getData();
                } else {
                    log.error("Failed to fetch search data: {}", result.getMsg());
                    throw new RuntimeException(result.getMsg());
                }
            } catch (Exception e) {
                log.error("realtimeFileParse invoke error on attempt {}", attempt + 1, e);
                if (attempt < maxRetries - 1) {
                    try {
                        Thread.sleep(retryInterval);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("Thread interrupted", ie);
                    }
                } else {
                    log.error("Max retries reached. realtimeFileParse invoke error", e);
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }


    private KbRepoSearchBody buildSearchBody(KbRepoBaseInfoPO kbRepoBaseInfoPO, List<KbRepoCollectionDTO> collectionDTOS, KbSearchRequest request) {
        List<Long> collectIds = collectionDTOS.stream().map(KbRepoCollectionDTO::getId).collect(Collectors.toList());
        return KbRepoSearchBody
            .builder()
            .embeddingModel(kbRepoBaseInfoPO.getVectorModelKey())
            .rerankModel(properties.getMilvus().getRerankModel())
            .tenantId(kbRepoBaseInfoPO.getTenantId())
            .searchMode(request.getSearchMode())
            .repoIdList(request.getRepoIds())
            .collectionIdList(collectIds)
            .questions(request.getQuestions())
            .needRerank(request.getNeedRerank())
            .needQaPairIntercept(request.getNeedQaPairIntercept())
            .needCasesIntercept(request.getNeedCasesIntercept())
            .topK(request.getTopK())
            .score(request.getScore())
            .build();
    }

    private List<KbSearchResultItem> convertToKbSearchResultItems(List<KbRepoDataDTO> repoDataDTOS, List<KbSearchData> searchData) {
        Map<String, KbSearchData> dataKeyMap = searchData.stream().collect(Collectors.toMap(KbSearchData::getDataKey, Function.identity()));
        List<Long> collectionIds = repoDataDTOS.stream().map(KbRepoDataDTO::getCollectionId).distinct().collect(Collectors.toList());
        List<KbRepoCollectionDTO> collectionDTOList = collectionRepository.selectCollectionByIds(collectionIds);
        Map<Long, KbRepoCollectionDTO> collectionMap = collectionDTOList.stream().collect(Collectors.toMap(KbRepoCollectionDTO::getId, Function.identity()));
        return VirtualThreadUtils.processInParallelWithExceptionHandling(
            repoDataDTOS,
            repoDataDTO -> {
                KbSearchData kbSearchData = dataKeyMap.get(repoDataDTO.getDataKey());
                KbRepoCollectionDTO collection = collectionMap.get(repoDataDTO.getCollectionId());
                // 文档来源需要生成可以下载的url
                String sourceUrl = "";
                if (Objects.nonNull(collection)) {
                    if (StrUtil.isNotBlank(collection.getExternalFileId())) {
                        try {
                            String originName = null;
                            if (collection.getExternalFileId().contains("/")) {
                                originName = collection.getExternalFileId().substring(collection.getExternalFileId().lastIndexOf("/") + 1);
                            } else {
                                originName = collection.getName();
                            }
                            sourceUrl = fileStorageOperation.getCachedPreSignedUrl(collection.getExternalFileId(), originName);
                        }catch (Exception e) {
                            log.error("获取知识集外链失败", e);
                        }
                    }
                }
                String question = repoDataDTO.getQuestion();
                if (DataTypeEnum.IMAGE.getCode().equals(repoDataDTO.getDataType())) {
                    try {
                        String imageUrl = JsonUtils.parseToMap(repoDataDTO.getExtData()).get("url");
                        question = "【" + repoDataDTO.getQuestion() + "】" + "\n下面是上面【】包裹的文字对应的图片,如果需要用到这个信息,将图片markdown链接返回去\n" + "![image](" + imageUrl + ")\n";
                    } catch (Exception ignored) {
                    }
                }
                return KbSearchResultItem.builder()
                    .repoId(repoDataDTO.getRepoId())
                    .collectionId(repoDataDTO.getCollectionId().toString())
                    .dataKey(repoDataDTO.getDataKey())
                    .indexKey(kbSearchData != null ? kbSearchData.getIndexKey() : "")
                    .question(question)
                    .answer(repoDataDTO.getAnswer())
                    .extData(repoDataDTO.getExtData())
                    .score(kbSearchData != null ? kbSearchData.getScore() : 0.0)
                    .sourceName(collection != null ? collection.getName() : "")
                    .sourceUrl(sourceUrl)
                    .build();
            }
        );
    }

    private KbSearchResponse invokeLocalSearch(KbRepoSearchBody searchBody) {
        Assert.notNull(searchBody.getEmbeddingModel(), "embeddingModel must not be null");
        if (Objects.equals(true, searchBody.getNeedRerank())) {
            Assert.notNull(searchBody.getRerankModel(), "rerankModel must not be null");
        }
        MilvusProperties milvusProperties = properties.getMilvus();
        // 如果开启标准问答对拦截 并且匹配分数到一定程度 直接返回
        if (Objects.equals(true, searchBody.getNeedQaPairIntercept())) {
            KbSearchResponse kbSearchResponse = searchQaPair(milvusProperties, searchBody);
            if (CollectionUtil.isNotEmpty(kbSearchResponse.getResults())) {
                return kbSearchResponse;
            }
        }
        // 如果开启标准问答对拦截 并且匹配分数到一定程度 直接返回
        if (Objects.equals(true, searchBody.getNeedCasesIntercept())) {
            KbSearchResponse kbSearchResponse = searchCases(milvusProperties, searchBody);
            if (CollectionUtil.isNotEmpty(kbSearchResponse.getResults())) {
                return kbSearchResponse;
            }
        }

        List<KbSearchData> searchData = switch (searchBody.getSearchMode()) {
            case SEMANTIC -> searchSemantic(milvusProperties, searchBody);
            case KEYWORD -> searchKeyword(milvusProperties, searchBody);
            case HYBRID -> searchHybrid(milvusProperties, searchBody);
        };
        return getCurrProxy().convertToKbSearchResponse(searchData);
    }

    private KbSearchResponse searchQaPair(MilvusProperties milvusProperties, KbRepoSearchBody searchBody) {
        // 获取嵌入模型
        EmbeddingModel embeddingModel = aiModelManager.getEmbeddingModel(searchBody.getEmbeddingModel());
        // 并行生成向量嵌入
        List<BaseVector> queryVectors = getCurrProxy().queryDenseVectors(searchBody.getQuestions(), embeddingModel);
        SearchResp searchResp = getCurrProxy().executeQaPairSearch(
            milvusProperties,
            milvusProperties.getSemanticField(),
            milvusProperties.getSemanticMetricType(),
            queryVectors,
            searchBody
        );
        // 获取初始搜索结果
        List<List<KbSearchQaPairData>> initialResults = searchResp.getSearchResults().stream()
            .flatMap(List::stream)
            .map(result -> KbSearchQaPairData.builder()
                .qaPairKey(result.getEntity().get("qa_pair_key").toString())
                .score(MilvusUtils.getResultSimilarity(result.getScore(), milvusProperties.getSemanticMetricType()))
                .question(result.getEntity().get("question").toString())
                .answer(result.getEntity().get("answer").toString())
                .repoId(Long.valueOf(result.getEntity().get("repo_id").toString()))
                .build())
            .collect(Collectors.groupingBy(KbSearchQaPairData::getQaPairKey))
            .values()
            .stream()
            .toList();
        // 满足某个分数以上的回答 直接返回
        for (List<KbSearchQaPairData> initialResultList : initialResults) {
            for (KbSearchQaPairData initialResult : initialResultList) {
                if (initialResult.getScore() >= milvusProperties.getQaPairInterceptScoreThreshold()) {
                    String answer = initialResult.getAnswer();
                    // 新增逻辑：处理图片占位符并替换为正常Markdown格式
                    String regex = "!\\[image]\\((.*?)\\)";
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(answer);
                    StringBuffer processedAnswer = new StringBuffer();
                    while (matcher.find()) {
                        String imagePath = matcher.group(1); // 提取括号中的路径参数
                        String externalUrl = fileActionService.getFileUrl(imagePath); // 调用服务获取外链
                        matcher.appendReplacement(processedAnswer, "![](" + externalUrl + ")");
                    }
                    matcher.appendTail(processedAnswer);
                    answer = processedAnswer.toString();

                    KbSearchResultItem resultItem = KbSearchResultItem.builder()
                        .repoId(initialResult.getRepoId())
                        .qaPairKey(initialResult.getQaPairKey())
                        .question(initialResult.getQuestion())
                        .answer(answer)
                        .score(initialResult.getScore())
                        .sourceName("标准问答库")
                        .build();
                    return KbSearchResponse.builder()
                        .results(List.of(resultItem))
                        .resultContents(List.of(resultItem.getAnswer()))
                        .requestParams(searchBody)
                        .build();
                }
            }
        }
        return KbSearchResponse.builder().build();
    }


    private KbSearchResponse searchCases(MilvusProperties milvusProperties, KbRepoSearchBody searchBody) {
        // 获取嵌入模型
        EmbeddingModel embeddingModel = aiModelManager.getEmbeddingModel(searchBody.getEmbeddingModel());
        // 并行生成向量嵌入
        List<BaseVector> queryVectors = getCurrProxy().queryDenseVectors(searchBody.getQuestions(), embeddingModel);
        // 处理稀疏向量
        List<BaseVector> querySparseVectors = searchBody.getQuestions().stream()
            .map(EmbeddedText::new)
            .collect(Collectors.toList());

        // 创建过滤表达式
        String filterExpr = createCasesFilterExpression(searchBody);
        // 获取混合检索的重排序器
        RerankerProperties reranker = milvusProperties.getReranker();
        BaseRanker ranker = switch (reranker.getStrategy()) {
            case WEIGHTED -> new WeightedRanker(List.of(reranker.getSemanticWeight(), reranker.getKeywordWeight()));
            case RRF -> new RRFRanker(reranker.getK());
        };
        // 构建混合搜索请求
        HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
            .collectionName(milvusProperties.getCollectionNameOfCases(searchBody.getEmbeddingModel()))
            .databaseName(milvusProperties.getDatabaseName())
            .searchRequests(List.of(
                // 语义搜索请求
                createSearchRequest(
                    milvusProperties.getSemanticField(),
                    queryVectors,
                    milvusProperties.getSemanticMetricType(),
                    filterExpr,
                    searchBody.getTopK()
                ),
                // 关键词搜索请求
                createSearchRequest(
                    "content_embeddings",
                    querySparseVectors,
                    IndexParam.MetricType.BM25,
                    filterExpr,
                    searchBody.getTopK()
                )
            ))
            .ranker(ranker)
            .topK(searchBody.getTopK())
            .outFields(COMMON_OUTPUT_FIELDS_CASES)
            .consistencyLevel(ConsistencyLevel.BOUNDED)
            .build();

        // 执行混合搜索
        SearchResp searchResp = getCurrProxy().hybridSearch(hybridSearchReq);

        // 获取初始搜索结果
        List<List<KbSearchCasesData>> initialResults = searchResp.getSearchResults().stream()
            .flatMap(List::stream)
            .map(result -> KbSearchCasesData.builder()
                .casesId(Long.valueOf(result.getEntity().get("cases_id").toString()))
                .score(MilvusUtils.getResultSimilarity(result.getScore(), milvusProperties.getSemanticMetricType()))
                .title(result.getEntity().get("title").toString())
                .content(result.getEntity().get("content").toString())
                .impact(result.getEntity().get("impact").toString())
                .repoId(Long.valueOf(result.getEntity().get("repo_id").toString()))
                .build())
            .collect(Collectors.groupingBy(KbSearchCasesData::getCasesId))
            .values()
            .stream()
            .toList();
        // 满足某个分数以上的回答 直接返回
        List<KbSearchResultItem> results = new ArrayList<>();
        List<String> resultContents = new ArrayList<>();
        for (List<KbSearchCasesData> initialResultList : initialResults) {
            Double casesInterceptScoreThreshold = milvusProperties.getCasesInterceptScoreThreshold();
            List<KbSearchCasesData> needProcessCases = initialResultList.stream().filter(data -> data.getScore() >= casesInterceptScoreThreshold).toList();
            if (CollUtil.isEmpty(needProcessCases)) {
                continue;
            }
            Set<Long> collect = needProcessCases.stream().map(KbSearchCasesData::getCasesId).collect(Collectors.toSet());
            List<KbRepoCasesDTO> kbRepoCasesDTOS = repositoryImpl.selectByIds(collect);
            Map<Long, KbRepoCasesDTO> casesMap = kbRepoCasesDTOS.stream().collect(Collectors.toMap(KbRepoCasesDTO::getId, Function.identity()));

            for (KbSearchCasesData initialResult : needProcessCases) {
                String content = initialResult.getContent();
                // 新增逻辑：处理图片占位符并替换为正常Markdown格式
                String regex = "!\\[image]\\((.*?)\\)";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(content);
                StringBuffer processedAnswer = new StringBuffer();
                while (matcher.find()) {
                    String imagePath = matcher.group(1); // 提取括号中的路径参数
                    String externalUrl = fileActionService.getFileUrl(imagePath); // 调用服务获取外链
                    matcher.appendReplacement(processedAnswer, "![](" + externalUrl + ")");
                }
                matcher.appendTail(processedAnswer);
                content = processedAnswer.toString();
                KbRepoCasesDTO casesDTO = casesMap.get(initialResult.getCasesId());
                KbSearchResultItem resultItem = KbSearchResultItem.builder()
                    .repoId(initialResult.getRepoId())
                    .casesId(initialResult.getCasesId())
                    .title(initialResult.getTitle())
                    .content(content)
                    .impact(initialResult.getImpact())
                    .rectify(casesDTO == null ? "" : casesDTO.getRectify())
                    .score(initialResult.getScore())
                    .sourceName("案例库")
                    .build();
                results.add(resultItem);
                resultContents.add(resultItem.getTitle());

            }
        }
        return KbSearchResponse.builder()
            .results(results)
            .resultContents(resultContents)
            .requestParams(searchBody).build();
    }

    private List<KbSearchData> searchHybrid(MilvusProperties milvusProperties, KbRepoSearchBody searchBody) {
        List<String> questions = searchBody.getQuestions();
        if (questions == null || questions.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取嵌入模型
        EmbeddingModel embeddingModel = aiModelManager.getEmbeddingModel(searchBody.getEmbeddingModel());

        // 并行处理密集向量
        List<BaseVector> queryDenseVectors = getCurrProxy().queryDenseVectors(searchBody.getQuestions(), embeddingModel);

        // 处理稀疏向量
        List<BaseVector> querySparseVectors = questions.stream()
            .map(EmbeddedText::new)
            .collect(Collectors.toList());

        // 创建过滤表达式
        String filterExpr = createFilterExpression(searchBody);

        // 获取混合检索的重排序器
        RerankerProperties reranker = milvusProperties.getReranker();
        BaseRanker ranker = switch (reranker.getStrategy()) {
            case WEIGHTED -> new WeightedRanker(List.of(reranker.getSemanticWeight(), reranker.getKeywordWeight()));
            case RRF -> new RRFRanker(reranker.getK());
        };

        // 构建混合搜索请求
        HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
            .collectionName(milvusProperties.getCollectionName(searchBody.getEmbeddingModel()))
            .databaseName(milvusProperties.getDatabaseName())
            .searchRequests(List.of(
                // 语义搜索请求
                createSearchRequest(
                    milvusProperties.getSemanticField(),
                    queryDenseVectors,
                    milvusProperties.getSemanticMetricType(),
                    filterExpr,
                    searchBody.getTopK()
                ),
                // 关键词搜索请求
                createSearchRequest(
                    milvusProperties.getKeywordField(),
                    querySparseVectors,
                    milvusProperties.getKeywordMetricType(),
                    filterExpr,
                    searchBody.getTopK()
                )
            ))
            .ranker(ranker)
            .topK(searchBody.getTopK())
            .outFields(COMMON_OUTPUT_FIELDS)
            .consistencyLevel(ConsistencyLevel.BOUNDED)
            .build();

        // 执行混合搜索
        SearchResp searchResp = getCurrProxy().hybridSearch(hybridSearchReq);

        // 处理搜索结果并返回
        return processSearchResults(milvusProperties.getSemanticMetricType(), searchResp, searchBody);
    }

    private List<KbSearchData> searchKeyword(MilvusProperties milvusProperties, KbRepoSearchBody searchBody) {
        List<BaseVector> queryVectors = searchBody.getQuestions().stream()
            .map(EmbeddedText::new)
            .collect(Collectors.toList());

        SearchResp searchResp = getCurrProxy().executeSearch(
            milvusProperties,
            milvusProperties.getKeywordField(),
            milvusProperties.getKeywordMetricType(),
            queryVectors,
            searchBody
        );

        return processSearchResults(milvusProperties.getKeywordMetricType(), searchResp, searchBody);
    }

    /**
     * 语义检索
     */
    private List<KbSearchData> searchSemantic(MilvusProperties milvusProperties, KbRepoSearchBody searchBody) {
        // 获取嵌入模型
        EmbeddingModel embeddingModel = aiModelManager.getEmbeddingModel(searchBody.getEmbeddingModel());

        // 并行生成向量嵌入
        List<BaseVector> queryVectors = getCurrProxy().queryDenseVectors(searchBody.getQuestions(), embeddingModel);

        SearchResp searchResp = getCurrProxy().executeSearch(
            milvusProperties,
            milvusProperties.getSemanticField(),
            milvusProperties.getSemanticMetricType(),
            queryVectors,
            searchBody
        );

        return processSearchResults(milvusProperties.getSemanticMetricType(), searchResp, searchBody);
    }

    private String createFilterExpression(KbRepoSearchBody searchBody) {
        return MilvusFluentFilterBuilder.create()
            .eq("tenant_id", searchBody.getTenantId())
            .in("repo_id", searchBody.getRepoIdList())
            .notIn("collection_id", searchBody.getCollectionIdList())
            .end();
    }

    private String createCasesFilterExpression(KbRepoSearchBody searchBody) {
        return MilvusFluentFilterBuilder.create()
            .eq("tenant_id", searchBody.getTenantId())
            .in("repo_id", searchBody.getRepoIdList())
            .end();
    }

    private AnnSearchReq createSearchRequest(
        String fieldName,
        List<BaseVector> vectors,
        IndexParam.MetricType metricType,
        String filterExpr,
        int topK) {
        return AnnSearchReq.builder()
            .vectorFieldName(fieldName)
            .vectors(vectors)
            .metricType(metricType)
            .expr(filterExpr)
            .topK(topK * 2)
            .build();
    }

    private List<KbSearchData> processSearchResults(IndexParam.MetricType metricType, SearchResp searchResp, KbRepoSearchBody searchBody) {
        int topK = searchBody.getTopK();
        double score = searchBody.getScore();
        boolean enableReranking = searchBody.getNeedRerank();
        // 获取初始搜索结果
        List<KbSearchData> initialResults = searchResp.getSearchResults().stream()
            .flatMap(List::stream)
            .map(result -> KbSearchData.builder()
                .dataKey(result.getEntity().get("data_key").toString())
                .score(MilvusUtils.getResultSimilarity(result.getScore(), metricType))
                .content(result.getEntity().get("content").toString())
                .indexKey(result.getEntity().get("index_key").toString())
                .build())
            //按indexKey去重
            .collect(Collectors.groupingBy(KbSearchData::getIndexKey))
            .values()
            .stream()
            .map(List::getFirst)
            .toList();

        if (enableReranking) {
            // 执行重排序
            try {
                initialResults = getCurrProxy().doRerank(initialResults, searchBody.getRerankModel(), searchBody.getQuestions());
            } catch (Exception e) {
                log.warn("Failed to rerank search results", e);
            }
        }
        return initialResults.stream()
            .collect(Collectors.toMap(
                KbSearchData::getDataKey,
                data -> data,
                (data1, data2) -> data1.getScore() > data2.getScore() ? data1 : data2
            ))
            .values()
            .stream()
            .sorted(Comparator.comparing(KbSearchData::getScore).reversed())
            .limit(topK)
            .filter(data -> data.getScore() >= score)
            .toList();
    }


    /**
     * 调用远程检索,暂时弃用,改为本地实现
     */
    private KbSearchResponse invokeRemoteSearch(KbRepoSearchBody searchBody) {
        int maxRetries = 3; // 最大重试次数
        int retryInterval = 1000; // 重试间隔时间（毫秒）

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                String url = properties.getKbInvokeUrl().getSearchUrl();
                TypeToken<String> typeToken = new TypeToken<String>() {
                };
                ResponseResult<String> result = invokeService.fetchPost(url, JsonUtils.toJson(searchBody), typeToken);
                if (result.isSuccess()) {
                    List<KbSearchData> searchData = JsonUtils.parseToList(result.getData(), KbSearchData.class);
                    return getCurrProxy().convertToKbSearchResponse(searchData);
                } else {
                    log.warn("Failed to fetch search data: {}", result.getMsg());
                }
            } catch (Exception e) {
                log.error("FileParseTaskHandler invoke error on attempt {}", attempt + 1, e);
                if (attempt < maxRetries - 1) {
                    try {
                        Thread.sleep(retryInterval);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("Thread interrupted", ie);
                    }
                } else {
                    log.error("Max retries reached. FileParseTaskHandler invoke error", e);
                    throw new RuntimeException(e);
                }
            }
        }
        return KbSearchResponse.builder().build();
    }

    @LogMonitor(key = "embedding", description = "向量嵌入", logResult = false, logParameters = false, order = 1)
    public List<BaseVector> queryDenseVectors(List<String> questions, EmbeddingModel embeddingModel) {
        return VirtualThreadUtils.processInParallelWithExceptionHandling(
            questions,
            question -> new FloatVec(embeddingModel.embed(question))
        );
    }

    @LogMonitor(key = "embeddingSearch", description = "知识库向量库检索", logResult = false, logParameters = false, order = 2)
    public SearchResp executeSearch(
        MilvusProperties milvusProperties,
        String annsField,
        IndexParam.MetricType metricType,
        List<BaseVector> queryVectors,
        KbRepoSearchBody searchBody) {
        LoadCollectionReq loadCollectionReq = LoadCollectionReq.builder()
            .collectionName(milvusProperties.getCollectionName(searchBody.getEmbeddingModel()))
            .build();
        milvusClient.loadCollection(loadCollectionReq);
        return milvusClient.search(SearchReq.builder()
            .collectionName(milvusProperties.getCollectionName(searchBody.getEmbeddingModel()))
            .databaseName(milvusProperties.getDatabaseName())
            .metricType(metricType)
            .annsField(annsField)
            .data(queryVectors)
            .outputFields(COMMON_OUTPUT_FIELDS)
            .filter(createFilterExpression(searchBody))
            .topK(searchBody.getTopK() * 2)
            .build());
    }

    @LogMonitor(key = "qaPairEmbeddingSearch", description = "问答对向量库检索", logResult = false, logParameters = false, order = 2)
    public SearchResp executeQaPairSearch(
        MilvusProperties milvusProperties,
        String annsField,
        IndexParam.MetricType metricType,
        List<BaseVector> queryVectors,
        KbRepoSearchBody searchBody) {
        LoadCollectionReq loadCollectionReq = LoadCollectionReq.builder()
            .collectionName(milvusProperties.getCollectionNameOfQaPair(searchBody.getEmbeddingModel()))
            .build();
        milvusClient.loadCollection(loadCollectionReq);
        return milvusClient.search(SearchReq.builder()
            .collectionName(milvusProperties.getCollectionNameOfQaPair(searchBody.getEmbeddingModel()))
            .databaseName(milvusProperties.getDatabaseName())
            .metricType(metricType)
            .annsField(annsField)
            .data(queryVectors)
            .outputFields(COMMON_OUTPUT_FIELDS_QA)
            .filter(createFilterExpression(searchBody))
            .topK(3)
            .build());
    }

    @LogMonitor(key = "casesEmbeddingSearch", description = "案例库向量库检索", logResult = false, logParameters = false, order = 2)
    public SearchResp executeCasesSearch(
        MilvusProperties milvusProperties,
        String annsField,
        IndexParam.MetricType metricType,
        List<BaseVector> queryVectors,
        KbRepoSearchBody searchBody) {
        LoadCollectionReq loadCollectionReq = LoadCollectionReq.builder()
            .collectionName(milvusProperties.getCollectionNameOfCases(searchBody.getEmbeddingModel()))
            .build();
        milvusClient.loadCollection(loadCollectionReq);
        return milvusClient.search(SearchReq.builder()
            .collectionName(milvusProperties.getCollectionNameOfCases(searchBody.getEmbeddingModel()))
            .databaseName(milvusProperties.getDatabaseName())
            .metricType(metricType)
            .annsField(annsField)
            .data(queryVectors)
            .outputFields(COMMON_OUTPUT_FIELDS_CASES)
            .filter(createCasesFilterExpression(searchBody))
            .topK(3)
            .build());
    }

    @LogMonitor(key = "embeddingSearch", description = "向量库检索", logResult = false, logParameters = false, order = 2)
    public SearchResp hybridSearch(HybridSearchReq hybridSearchReq) {
        return milvusClient.hybridSearch(hybridSearchReq);
    }

    @LogMonitor(key = "rerank", description = "重排序", logResult = false, logParameters = false, order = 3)
    public List<KbSearchData> doRerank(List<KbSearchData> initialResults, String
        rerankModel, List<String> questions) {
        AIProviderProperties rerankProperties = aiModelManager.getProperty(rerankModel);

        List<String> contents = initialResults.stream().map(KbSearchData::getContent).toList();

        // 保存每个数据的得分累加值
        Map<String, Double> dataKeyToScore = new HashMap<>();
        Map<String, Integer> dataKeyToCount = new HashMap<>();

        List<CompletableFuture<List<RerankResult>>> futures = questions.stream()
            .map(question -> VirtualThreadUtils.supplyAsync(() -> {
                RerankRequest request = new RerankRequest(
                    question,
                    contents,
                    false,
                    1024,
                    80,
                    rerankProperties.getModel(),
                    contents.size()
                );
                // 调用外部 API 获取重排序的结果
                return restClient.post()
                    .uri(rerankProperties.getBaseUrl() + rerankProperties.getRerankPath())
                    .header("Authorization", "Bearer " + rerankProperties.getApiKey())
                    .header("Content-Type", "application/json")
                    .body(request)
                    .retrieve()
                    .body(RerankResponse.class)
                    .results();
            }))
            .collect(Collectors.toList());

        List<List<RerankResult>> allResults = VirtualThreadUtils.waitForAll(futures);

        // 处理所有结果
        for (List<RerankResult> rerankedList : allResults) {
            if (rerankedList == null) {
                continue;
            }
            for (RerankResult result : rerankedList) {
                KbSearchData rerankedData = initialResults.get(result.index());
                String dataKey = rerankedData.getDataKey();
                // 累加得分
                dataKeyToScore.merge(dataKey, result.relevanceScore(), Double::sum);
                // 累加计数
                dataKeyToCount.merge(dataKey, 1, Integer::sum);
            }
        }

        // 计算每条数据的加权得分 (每个问题的得分占比一样)
        return initialResults.stream()
            .peek(data -> {
                double totalScore = dataKeyToScore.getOrDefault(data.getDataKey(), 0.0);
                int count = dataKeyToCount.getOrDefault(data.getDataKey(), 1);
                double averageScore = totalScore / count;  // 计算加权平均得分
                // 更新得分
                data.setScore(averageScore);
            })
            .collect(Collectors.toList());
    }

    @LogMonitor(key = "kbRecall", description = "知识召回", logResult = false, logParameters = false, order = 4)
    public KbSearchResponse convertToKbSearchResponse(List<KbSearchData> searchData) {
        if (CollUtil.isNotEmpty(searchData)) {
            Set<String> dataKeys = searchData.stream().map(KbSearchData::getDataKey).collect(Collectors.toSet());

            // 返回对象 查询dataKey对应的数据
            List<KbRepoDataDTO> repoDataDTOS = dataQueryService.selectDataByKeys(dataKeys);
            // 转换为 KbSearchResultItem 列表
            List<KbSearchResultItem> resultItems = convertToKbSearchResultItems(repoDataDTOS, searchData);
            // 分值排序
            resultItems.sort(Comparator.comparing(KbSearchResultItem::getScore).reversed());
            // 组装KbSearchResponse返回
            return KbSearchResponse.builder()
                .results(resultItems)
                .resultContents(resultItems.stream().map(KbSearchResultItem::getQuestion).collect(Collectors.toList()))
                .build();
        }
        return KbSearchResponse.builder().build();
    }

    private KbRepoSearchActionService getCurrProxy() {
        return SpringUtil.getBean(KbRepoSearchActionService.class);
    }

    private KbRepoTaskOfFileParseBody buildRealtimeFileParseBody(String externalFileId, String
        externalFileUrl, String fileName, String contentType) {
        SsoUserAuthInfoDTO ssoUserAuthInfoDTO = CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.get();
        Long tenantId = (ssoUserAuthInfoDTO == null || ssoUserAuthInfoDTO.getTenantId() == null)? 1L : ssoUserAuthInfoDTO.getTenantId();
        return KbRepoTaskOfFileParseBody
            .builder()
            .tenantId(tenantId)
            .externalFileId(externalFileId)
            .externalFileUrl(externalFileUrl)
            .fileName(fileName)
            .contentType(contentType)
            .deepParse(false)
            .build();
    }

    public record RerankRequest(
        String query,
        List<String> documents,
        @JsonProperty("return_documents")
        boolean returnDocuments,
        @JsonProperty("max_chunks_per_doc")
        int maxChunksPerDoc,
        @JsonProperty("overlap_tokens")
        int overlapTokens,
        String model,
        @JsonProperty("top_n")
        int topN
    ) {
    }

    public record RerankResponse(
        List<RerankResult> results
    ) {
    }

    public record RerankResult(
        int index,
        @JsonProperty("relevance_score")
        double relevanceScore,
        String document
    ) {
    }

}
