package cn.genn.ai.hub.app.application.dto.rtc;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * RTC代理类型枚举
 * <AUTHOR>
 */
@Getter
public enum RTCAgentType {

    /**
     * 通用代理类型
     */
    GENERAL("general", "通用智能体"),

    /**
     * 非通用代理类型
     */
    SPECIFIC("specific", "定制智能体"),

    /**
     * 隐患7日分析
     */
    ANALYSIS("analysis", "隐患7日分析");

    @JsonValue
    private final String code;
    private final String description;

    RTCAgentType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举值
     * @param code 代码
     * @return 枚举值
     */
    public static RTCAgentType fromCode(String code) {
        for (RTCAgentType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown RTC agent type code: " + code);
    }
}
