package cn.genn.ai.hub.plugin.impl.kb.model;

import cn.genn.ai.hub.core.api.kb.SearchFilterParam;
import cn.genn.ai.hub.core.api.kb.SearchModeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * AI知识库检索输入参数
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KbSearchInputParam {

    /**
     * 知识库id集合,支持传入多个知识库id
     */
    private List<Long> repoIds;

    /**
     * 检索模式，默认为语义检索
     */
    @Builder.Default
    private SearchModeEnum searchMode = SearchModeEnum.SEMANTIC;

    /**
     * 用户问题
     */
    private String question;

    /**
     * 是否重排序，对检索结果进行重新排序
     */
    @Builder.Default
    private Boolean rerank = false;

    /**
     * 搜索过滤参数，包含引用上限，最低相关度等
     */
    @Builder.Default
    private SearchFilterParam filterParam = SearchFilterParam.builder().build();

    /**
     * 问题优化参数，包含是否启用，优化模型，优化提示词等
     */
    @Builder.Default
    private QuestionOptimizationParam optimizationParam = QuestionOptimizationParam.builder().build();

    /**
     * 是否开启标准问答对拦截
     */
    @Builder.Default
    private Boolean needQaPairIntercept = false;
    /**
     * 是否开启案例库拦截
     */
    @Builder.Default
    private Boolean needCasesIntercept = false;
}
