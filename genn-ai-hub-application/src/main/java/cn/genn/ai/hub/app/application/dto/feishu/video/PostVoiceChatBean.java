package cn.genn.ai.hub.app.application.dto.feishu.video;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PostVoiceChatBean {
    @JsonProperty("AppId")
    String appId;
    @JsonProperty("RoomId")
    String roomId;
    @JsonProperty("TaskId")
    String taskId;
    @JsonProperty("Config")
    Config config;
    @JsonProperty("AgentConfig")
    AgentConfig agentConfig;

    @JsonProperty("Command")
    String command;
    @JsonProperty("Message")
    String message;
    @JsonProperty("InterruptMode")
    Integer interruptMode;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Config {
        @JsonProperty("ASRConfig")
        ASRConfig asrConfig;
        @JsonProperty("TTSConfig")
        TTSConfig ttsConfig;
        @JsonProperty("LLMConfig")
        LLMConfig llmConfig;
        @JsonProperty("SubtitleConfig")
        SubtitleConfig subtitleConfig;
        @JsonProperty("FunctionCallingConfig")
        FunctionCallingConfig functionCallingConfig;
        @JsonProperty("InterruptMode")
        Integer interruptMode;

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class ASRConfig {
            @JsonProperty("Provider")
            String provider;//语音识别服务提供商。该参数固定取值：volcano，表示仅支持火山引擎语音识别服务
            @JsonProperty("ProviderParams")
            ProviderParams providerParams;
            @JsonProperty("VADConfig")
            VADConfig vdcConfig;
            @JsonProperty("VolumeGain")
            Float volumeGain;
            @JsonProperty("InterruptConfig")
            InterruptConfig interruptConfig;
            @JsonProperty("TurnDetectionMode")
            Integer turnDetectionMode;


            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class ProviderParams {
                @JsonProperty("Mode")
                String mode;
                @JsonProperty("AppId")
                String appId;
                @JsonProperty("AccessToken")
                String accessToken;
                @JsonProperty("ApiResourceId")
                String apiResourceId;
                @JsonProperty("StreamMode")
                Integer streamMode;
                @JsonProperty("context")
                String context;
                @JsonProperty("boosting_table_id")
                String boostingTableId;
                @JsonProperty("boosting_table_name")
                String boostingTableName;
                @JsonProperty("context_history_length")
                Integer contextHistoryLength;
                @JsonProperty("correct_table_id")
                String correctTableId;
                @JsonProperty("correct_table_name")
                String correctTableName;
                @JsonProperty("Cluster")
                String cluster;

            }

            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class VADConfig {
                @JsonProperty("SilenceTime")
                Integer silenceTime;

                @JsonProperty("AIVAD")
                Boolean aivad;

            }

            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class InterruptConfig {
                @JsonProperty("InterruptSpeechDuration")
                Integer interruptSpeechDuration;
                @JsonProperty("InterruptKeywords")
                String[] interruptKeywords;

            }
        }

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class TTSConfig {
            @JsonProperty("IgnoreBracketText")
            Integer[] ignoreBracketText;
            @JsonProperty("Provider")
            String provider;
            @JsonProperty("ProviderParams")
            ProviderParams providerParams;

            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class ProviderParams {
                @JsonProperty("app")
                App app;
                @JsonProperty("audio")
                Audio audio;
                @JsonProperty("Additions")
                Additions additions;
                @JsonProperty("MiniMax")
                MiniMax miniMax;
                @JsonProperty("ResourceId")
                String resourceId;
                @JsonProperty("stream")
                Boolean stream;
                @JsonProperty("language_booststring")
                String languageBooststring;

                @Data
                @JsonInclude(JsonInclude.Include.NON_NULL)
                public static class App {
                    @JsonProperty("appid")
                    String appId;
                    @JsonProperty("cluster")
                    String cluster;
                    @JsonProperty("token")
                    String token;

                }

                @Data
                @JsonInclude(JsonInclude.Include.NON_NULL)
                public static class Audio {
                    @JsonProperty("voice_type")
                    String voiceType;
                    @JsonProperty("speed_ratio")
                    Float speedRatio;
                    @JsonProperty("volume_ratio")
                    Float volumeRatio;
                    @JsonProperty("pitch_ratio")
                    Float pitchRatio;
                    @JsonProperty("pitch_rate")
                    Integer pitchRate;
                    @JsonProperty("speech_rate")
                    Integer speechRate;
                }

                @Data
                @JsonInclude(JsonInclude.Include.NON_NULL)
                public static class Additions {
                    @JsonProperty("enable_latex_tn")
                    Boolean enableLatexTn;
                    @JsonProperty("disable_markdown_filter")
                    Boolean disableMarkdownFilter;
                    @JsonProperty("enable_language_detector")
                    Boolean enableLanguageDetector;
                }

                @Data
                @JsonInclude(JsonInclude.Include.NON_NULL)
                public static class MiniMax {
                    @JsonProperty("Authorization")
                    String authorization;
                    @JsonProperty("Groupid")
                    String groupId;
                    @JsonProperty("model")
                    String model;
                    @JsonProperty("URL")
                    String URL;
                    @JsonProperty("voice_setting")
                    VoiceSetting voiceSetting;
                    @JsonProperty("pronunciation_dict")
                    PronunciationDict pronunciationDict;
                    @JsonProperty("timber_weights")
                    TimberWeight[] timberWeights;

                    @Data
                    @JsonInclude(JsonInclude.Include.NON_NULL)
                    public static class VoiceSetting {
                        @JsonProperty("speed")
                        Float speed;
                        @JsonProperty("vol")
                        Float vol;
                        @JsonProperty("pitch")
                        Float pitch;
                        @JsonProperty("voice_id")
                        String voiceId;
                    }

                    @Data
                    @JsonInclude(JsonInclude.Include.NON_NULL)
                    public static class PronunciationDict {
                        @JsonProperty("tone")
                        String[] tone;
                    }

                    @Data
                    @JsonInclude(JsonInclude.Include.NON_NULL)
                    public static class TimberWeight {
                        @JsonProperty("voice_id")
                        String voiceId;
                        @JsonProperty("weight")
                        Integer weight;
                    }
                }
            }
        }

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class LLMConfig {
            @JsonProperty("Mode")
            String mode;
            @JsonProperty("EndPointId")
            String endPointId;
            @JsonProperty("BotId")
            String botId;
            @JsonProperty("Temperature")
            Float temperature;
            @JsonProperty("MaxTokens")
            Integer maxTokens;
            @JsonProperty("TopP")
            Float topP;
            @JsonProperty("SystemMessages")
            String[] systemMessages;
            @JsonProperty("UserPrompts")
            UserPrompts[] userPrompts;
            @JsonProperty("HistoryLength")
            Integer historyLength;
            @JsonProperty("Tools")
            Tool[] tools;
            @JsonProperty("Prefill")
            Boolean prefill;
            @JsonProperty("VisionConfig")
            VisionConfig visionConfig;
            @JsonProperty("CozeBotConfig")
            CozeBotConfig cozeBotConfig;
            @JsonProperty("URL")
            String URL;
            @JsonProperty("ModelName")
            String modelName;
            @JsonProperty("APIKey")
            String APIKey;
            @JsonProperty("Feature")
            String feature;
            @JsonProperty("Custom")
            String custom;


            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class UserPrompts {
                @JsonProperty("Role")
                String role;
                @JsonProperty("Content")
                String content;
            }

            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class Tool {
                @JsonProperty("type")
                String type;
                @JsonProperty("function")
                Function function;

                @Data
                @JsonInclude(JsonInclude.Include.NON_NULL)
                public static class Function {
                    @JsonProperty("name")
                    String name;
                    @JsonProperty("description")
                    String description;
                    @JsonProperty("parameters")
                    JSONObject parameters;
                }
            }

            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class VisionConfig {
                @JsonProperty("Enable")
                Boolean enable;
                @JsonProperty("SnapshotConfig")
                SnapshotConfig snapshotConfig;
                @JsonProperty("StorageConfig")
                StorageConfig storageConfig;

                @Data
                @JsonInclude(JsonInclude.Include.NON_NULL)
                public static class SnapshotConfig {
                    /**
                     * 0 主流
                     * 1 屏幕流
                     */
                    @JsonProperty("StreamType")
                    Integer streamType = 0;
                    /**
                     * high：高细节模式。适用于需要理解图像细节信息的场景，如对图像的多个局部信息/特征提取、复杂/丰富细节的图像理解等场景，理解更全面。
                     * low：低细节模式。适用于简单的图像分类/识别、整体内容理解/描述等场景，理解更快速。
                     * auto：自动模式。根据图片分辨率，自动选择适合的模式。
                     * 默认值为 auto。
                     */
                    @JsonProperty("ImageDetail")
                    String imageDetail = "high";
                    /**
                     * 送入大模型视频帧高度，取值范围为 [0, 1792]，单位为像素。
                     */
                    @JsonProperty("Height")
                    Integer height = 640;
                    /**
                     * 相邻截图之间的间隔时间，取值范围为 [100, 5000]，单位为毫秒。默认值为 1000。
                     */
                    @JsonProperty("Interval")
                    Integer interval = 1000;
                    /**
                     * 单次送大模型图片数。取值范围为 [0, 50]。
                     */
                    @JsonProperty("ImagesLimit")
                    Integer imagesLimit = 1;

                }

                @Data
                @JsonInclude(JsonInclude.Include.NON_NULL)
                public static class StorageConfig {
                    @JsonProperty("Type")
                    Integer type;
                    @JsonProperty("TosConfig")
                    TosConfig tosConfig;

                    @Data
                    @JsonInclude(JsonInclude.Include.NON_NULL)
                    public static class TosConfig {
                        @JsonProperty("AccountId")
                        String accountId;
                        @JsonProperty("Region")
                        Integer region;
                        @JsonProperty("Bucket")
                        String bucket;

                    }
                }
            }

            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class CozeBotConfig {
                @JsonProperty("Url")
                String url;
                @JsonProperty("BotId")
                String botId;
                @JsonProperty("APIKey")
                String apiKey;
                @JsonProperty("UserId")
                String userId;
                @JsonProperty("HistoryLength")
                Integer historyLength;
                @JsonProperty("Prefill")
                Boolean prefill;
                @JsonProperty("EnableConversation")
                Boolean enableConversation;

            }

            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class CustomConfig {
                @JsonProperty("appId")
                String appId;
                @JsonProperty("chatId")
                String chatId;
                @JsonProperty("onlyAnswer")
                Boolean onlyAnswer;
                @JsonProperty("gennAiToken")
                String gennAiToken;
            }
        }

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class SubtitleConfig {
            @JsonProperty("DisableRTSSubtitle")
            Boolean disableRTSSubtitle;
            @JsonProperty("ServerMessageUrl")
            String serverMessageUrl;
            @JsonProperty("ServerMessageSignature")
            String serverMessageSignature;
            @JsonProperty("SubtitleMode")
            Integer subtitleMode;

        }

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class FunctionCallingConfig {
            @JsonProperty("ServerMessageUrl")
            String serverMessageUrl;
            @JsonProperty("ServerMessageSignature")
            String serverMessageSignature;

        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AgentConfig {
        @JsonProperty("TargetUserId")
        String[] targetUserId;
        @JsonProperty("WelcomeMessage")
        String welcomeMessage;
        @JsonProperty("UserId")
        String userId;
        @JsonProperty("EnableConversationStateCallback")
        Boolean enableConversationStateCallback;
        @JsonProperty("ServerMessageSignatureForRTS")
        String serverMessageSignatureForRTS;
        @JsonProperty("ServerMessageURLForRTS")
        String serverMessageURLForRTS;
        @JsonProperty("Burst")
        Burst burst;

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class Burst {
            @JsonProperty("Enable")
            Boolean enable;
            @JsonProperty("BufferSize")
            Integer bufferSize;
            @JsonProperty("Interval")
            Integer interval;

        }
    }
}
