package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.dto.GewuDocumentAddResult;
import cn.genn.ai.hub.app.application.dto.KbRepoCollectionDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import cn.genn.ai.hub.app.application.dto.request.GewuDocumentAddCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionQuery;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionSheetCreateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionTextOfFileCreateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionTextOfUrlCreateCommand;
import cn.genn.ai.hub.app.application.enums.CollectionTypeEnum;
import cn.genn.ai.hub.app.application.enums.TrainingModelEnum;
import cn.genn.ai.hub.app.application.enums.TrainingTypeEnum;
import cn.genn.ai.hub.app.application.service.action.KbRepoBaseInfoActionService;
import cn.genn.ai.hub.app.application.service.action.KbRepoCollectionActionService;
import cn.genn.ai.hub.app.application.service.action.KbRepoFileActionService;
import cn.genn.ai.hub.app.application.service.query.KbRepoCollectionQueryService;
import cn.genn.ai.hub.app.infrastructure.exception.GewuException;
import cn.genn.ai.hub.app.infrastructure.exception.MessageCode;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class GewuDocumentService {

    private final KbRepoCollectionQueryService collectionQueryService;
    private final KbRepoCollectionActionService collectionActionService;
    private final KbRepoBaseInfoRepositoryImpl baseInfoRepository;
    private final KbRepoCollectionQueryService kbRepoCollectionQueryService;
    private final KbRepoBaseInfoActionService kbRepoBaseInfoActionService;
    private final KbRepoFileActionService fileActionService;

    /**
     * 查询用户文档列表
     */
    public PageResultDTO<KbRepoCollectionDTO> queryDocuments(KbRepoCollectionQuery query) {
        // 获取gewu知识库
        KbRepoBaseInfoPO existingKb = baseInfoRepository.getGewuKnowledgeBaseByUserId(CurrentUserHolder.getUserId());
        if (ObjUtil.isNull(existingKb)) {
            kbRepoBaseInfoActionService.createDefaultGewuKnowledgeBase();
            return PageResultDTO.buildPageResult(CollectionUtil.newArrayList(), query.getPageNo(), query.getPageSize());
        }
        query.setRepoId(existingKb.getId());
        return kbRepoCollectionQueryService.page(query);
    }

    /**
     * 添加通用文档（统一接口）
     */
    public GewuDocumentAddResult addDocument(GewuDocumentAddCommand command) {
        try {
            Long gewuKbId = getGewuKbId();

            // 1. 先调用uploadLog记录文件信息，返回文件ID
            Long fileId = fileActionService.uploadLog(command.getFileInfo());

            // 2. 根据文档类型创建相应的数据集
            if ("sheet".equals(command.getType())) {
                return createSheetCollection(gewuKbId, fileId, command);
            } else {
                return createTextCollection(gewuKbId, fileId, command);
            }
        } catch (Exception e) {
            log.error("添加文档失败", e);
            throw new BusinessException(MessageCode.KB_REPO_COLLECTION_CREATE_FAIL);
        }
    }

    /**
     * 创建文本数据集
     */
    private GewuDocumentAddResult createTextCollection(Long repoId, Long fileId, GewuDocumentAddCommand command) {
        KbRepoCollectionTextOfFileCreateCommand createCommand = KbRepoCollectionTextOfFileCreateCommand.builder()
            .repoId(repoId)
            .fileId(fileId)
            .name(command.getName())
            .collectionType(CollectionTypeEnum.FILE)
            .trainingType(TrainingTypeEnum.AUTO)
            .handleType(TrainingModelEnum.CHUNK) // 默认直接分段
            .chunkSize(512) // 默认分块大小
            .deepParse(false) // 默认不深度解析
            .smartChunkSummary(false) // 默认不智能分块摘要
            .tenantId(CurrentUserHolder.getTenantId()) // 设置租户ID
            .build();

        Long collectionId = collectionActionService.createTextCollectionOfFile(createCommand);

        return GewuDocumentAddResult.builder()
            .documentId(collectionId)
            .name(createCommand.getName())
            .status("SUCCESS")
            .message("文档添加成功")
            .build();
    }

    /**
     * 创建表格数据集
     */
    private GewuDocumentAddResult createSheetCollection(Long repoId, Long fileId, GewuDocumentAddCommand command) {
        KbRepoCollectionSheetCreateCommand createCommand = KbRepoCollectionSheetCreateCommand.builder()
            .repoId(repoId)
            .fileId(fileId)
            .name(command.getName())
            .sheetIndex(0) // 默认第一个sheet
            .collectionType(CollectionTypeEnum.SHEET)
            .trainingType(TrainingTypeEnum.AUTO)
            .handleType(TrainingModelEnum.CHUNK) // 默认直接分段
            .chunckSize(1) // 默认1行作为一个分块
            .smartChunkSummary(false) // 默认不智能分块摘要
            .tenantId(CurrentUserHolder.getTenantId()) // 设置租户ID
            .build();

        Long collectionId = collectionActionService.createSheetColl(createCommand);

        return GewuDocumentAddResult.builder()
            .documentId(collectionId)
            .name(createCommand.getName())
            .status("SUCCESS")
            .message("表格添加成功")
            .build();
    }

    /**
     * 处理URL导入
     */
    public GewuDocumentAddResult handleUrlImport(String url, String name, String description) {
        try {
            Long gewuKbId = getGewuKbId();
            KbRepoCollectionTextOfUrlCreateCommand command = KbRepoCollectionTextOfUrlCreateCommand.builder()
                .repoId(gewuKbId)
                .name(name != null ? name : "网页文档")
                .url(url)
                .build();

            Long collectionId = collectionActionService.createTextCollectionOfUrl(command);

            return GewuDocumentAddResult.builder()
                .documentId(collectionId)
                .name(command.getName())
                .status("SUCCESS")
                .message("网页文档添加成功")
                .build();
        } catch (Exception e) {
            log.error("URL导入失败", e);
            throw new GewuException("GEWU_002", "URL导入失败: " + e.getMessage());
        }
    }

    /**
     * 处理飞书文档
     */
    private GewuDocumentAddResult handleFeishuDoc(String appId, String docToken, Long repoId, String name, String description) {
        Long gewuKbId = getGewuKbId();
        //todo:如果是飞书类型的,需要检查飞书基础config是否完善;
        // 这里需要调用飞书文档同步逻辑
        // 暂时抛出异常，需要根据实际的飞书服务来实现
        throw new GewuException("GEWU_003", "飞书文档功能暂未实现");
    }

    /**
     * 处理飞书表格
     */
    private GewuDocumentAddResult handleFeishuSheet(String sheetToken, String title, Long repoId, String name, String description) {
        Long gewuKbId = getGewuKbId();
        //todo:如果是飞书类型的,需要检查飞书基础config是否完善;
        // 这里需要调用飞书表格同步逻辑
        // 暂时抛出异常，需要根据实际的飞书服务来实现
        throw new GewuException("GEWU_003", "飞书表格功能暂未实现");
    }

    private Long getGewuKbId() {
        // 获取gewu知识库
        KbRepoBaseInfoPO existingKb = baseInfoRepository.getGewuKnowledgeBaseByUserId(CurrentUserHolder.getUserId());
        if (ObjUtil.isNull(existingKb)) {
            Long kbId = kbRepoBaseInfoActionService.createDefaultGewuKnowledgeBase();
            return kbId;
        }
        return existingKb.getId();
    }
}
