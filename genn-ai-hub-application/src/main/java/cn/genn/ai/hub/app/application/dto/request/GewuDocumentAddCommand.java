package cn.genn.ai.hub.app.application.dto.request;

import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "格物文档添加命令")
public class GewuDocumentAddCommand implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "文档类型：text-文本，sheet-表格")
    @NotBlank(message = "文档类型不能为空")
    private String type;
    
    @Schema(description = "文档名称")
    @NotBlank(message = "文档名称不能为空")
    private String name;
    
    @Schema(description = "文档描述")
    private String description;
    
    @Schema(description = "文件信息")
    @NotNull(message = "文件信息不能为空")
    private KbRepoFileDTO fileInfo;
}
