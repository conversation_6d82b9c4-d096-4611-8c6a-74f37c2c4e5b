package cn.genn.ai.hub.app.application.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "格物文档URL导入命令")
public class GewuDocumentUrlCommand implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "网页链接")
    @NotBlank(message = "URL不能为空")
    private String url;
    
    @Schema(description = "文档名称")
    private String name;
    
    @Schema(description = "文档描述")
    private String description;
}
