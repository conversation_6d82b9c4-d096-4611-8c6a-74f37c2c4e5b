package cn.genn.ai.hub.plugin.impl.kb.client;

import cn.genn.ai.hub.core.api.kb.*;
import cn.genn.ai.hub.plugin.common.aop.LogMonitor;
import cn.genn.ai.hub.plugin.impl.kb.model.KbCasesInputParam;
import cn.genn.ai.hub.plugin.impl.kb.model.KbSearchInputParam;
import cn.genn.ai.hub.plugin.impl.kb.model.KbSearchOutputParam;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 后续如果插件拆分,容器中有KbAbilityApi的feignClient实现即可
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class KbAbilityClient {

    private final KbAbilityApi kbAbilityApi;

    @LogMonitor(key = "kbSearchRemote", description = "知识库检索")
    public KbSearchOutputParam search(KbSearchInputParam inputParam, List<String> questions) {
        KbSearchRequest searchRequest = KbSearchRequest.builder()
            .repoIds(inputParam.getRepoIds())
            .topK(inputParam.getFilterParam().getMaxReferences())
            .score(inputParam.getFilterParam().getMinRelevanceScore())
            .searchMode(inputParam.getSearchMode())
            .needRerank(inputParam.getRerank())
            .needQaPairIntercept(inputParam.getNeedQaPairIntercept())
            .needCasesIntercept(inputParam.getNeedCasesIntercept())
            .questions(questions)
            .build();
        KbSearchResponse search = kbAbilityApi.search(searchRequest);
        if (search == null || CollUtil.isEmpty(search.getResults())) {
            return KbSearchOutputParam.builder()
                .resultCount(0)
                .build();
        }
        return KbSearchOutputParam.builder()
            .items(search.getResults())
            .itemContents(search.getResultContents())
            .resultCount(search.getResults().size())
            .build();
    }

    @LogMonitor(key = "batchCreateCases", description = "知识库-创建案例库内容")
    public Boolean batchCreateCases(KbCasesInputParam inputParam) {
        KbCreateCasesRequest createCasesRequest = KbCreateCasesRequest.builder()
            .repoId(inputParam.getRepoId())
            .casesInfos(inputParam.getCasesInfos())
            .build();
       return kbAbilityApi.batchCreateCases(createCasesRequest);
    }

    public List<Term> listTerms(List<Long> repoIds) {
        return kbAbilityApi.listTerms(repoIds);
    }
}
