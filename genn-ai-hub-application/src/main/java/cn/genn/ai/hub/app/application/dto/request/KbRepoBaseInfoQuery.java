package cn.genn.ai.hub.app.application.dto.request;

import cn.genn.ai.hub.app.application.enums.RepoTypeEnum;
import cn.genn.core.model.page.PageSortQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import cn.genn.ai.hub.app.application.enums.KbRepoSourceEnum;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * KbRepoBaseInfo查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KbRepoBaseInfoQuery extends PageSortQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "标签id列表")
    private List<Long> tagIds;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "知识库类型")
    private RepoTypeEnum repoType;

    @Schema(description = "团队id，如果为空，代表是个人空间创建")
    private Long teamId;

    @Schema(description = "知识库名称")
    private String name;
}

