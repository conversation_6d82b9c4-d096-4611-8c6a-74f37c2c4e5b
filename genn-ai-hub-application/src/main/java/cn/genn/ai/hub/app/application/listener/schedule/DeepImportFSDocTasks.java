package cn.genn.ai.hub.app.application.listener.schedule;

import cn.genn.ai.hub.app.application.assembler.FSAssembler;
import cn.genn.ai.hub.app.application.command.FSDocSyncCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoCollectionDTO;
import cn.genn.ai.hub.app.application.dto.feishu.FSAppConfig;
import cn.genn.ai.hub.app.application.dto.feishu.FSSyncParams;
import cn.genn.ai.hub.app.application.dto.feishu.NodeData;
import cn.genn.ai.hub.app.application.enums.feishu.FSFileType;
import cn.genn.ai.hub.app.application.service.FSWikiFileService;
import cn.genn.ai.hub.app.domain.channel.service.FeishuClientService;
import cn.genn.ai.hub.app.infrastructure.config.GennRequestContext;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepCollectionRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.lark.oapi.Client;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceReq;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceResp;
import com.lark.oapi.service.wiki.v2.model.Node;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DeepImportFSDocTasks extends IJobHandler {

    @Resource
    protected KbRepoBaseInfoRepositoryImpl repoBaseInfoRepository;

    @Resource
    protected KbRepCollectionRepositoryImpl collectionRepository;

    @Resource
    private FSWikiFileService wikiFileService;
    @Resource
    private FSAssembler fsaAssembler;
    @Resource
    private FeishuClientService clientService;

    private Params params;

    @Data
    public static class Params {
        // repo = 知识库  coll = 数据集
        private String type;
        // For userInput
        private List<Long> ids;
        // For userSelect
        private boolean needUpdate;

        private Long tenantId;
    }

    /**
     * TODO
     *
     * @throws Exception
     */
    @Override
    public void execute() throws Exception {
        log.info("DeepImportFSDocTasks begin");
        String jobParam = XxlJobHelper.getJobParam();
        ;
        this.params = JsonUtils.parse(jobParam, Params.class);
        if (this.params != null) {
            log.info("DeepImportFSDocTasks process param {}", jobParam);
            try {
                List<Long> processIds = this.params.getIds();
                switch (this.params.getType()) {
                    case "repo" -> processRepo(processIds);
                    case "coll" -> processColl(processIds);
                }
            } catch (Exception e) {
                log.error("同步飞书文档异常 ", e);
            }
        }
        log.info("DeepImportFSDocTasks end");
    }

    private void processRepo(List<Long> repoIds) {
        List<KbRepoBaseInfoPO> fsRepoInfos = repoBaseInfoRepository.getFSRepoInfos(repoIds);
        processFSRepo(fsRepoInfos);
    }

    private void processFSRepo(List<KbRepoBaseInfoPO> repoInfos) {
        if (CollUtil.isEmpty(repoInfos)) {
            return;
        }
        for (KbRepoBaseInfoPO repoInfo : repoInfos) {
            GennRequestContext.initTenantId(repoInfo.getTenantId());
            try {
                List<KbRepoCollectionDTO> sourceCllDTOs = collectionRepository.collIdsByRepoId(repoInfo.getId());
                processColl(repoInfo, sourceCllDTOs);

            } catch (Exception e) {
                log.error("同步飞书文档异常 ", e);
            } finally {
                GennRequestContext.clear();
            }
        }
    }

    private void processColl(KbRepoBaseInfoPO repoInfo, List<KbRepoCollectionDTO> sourceCllDTOs) {
        if (CollUtil.isEmpty(sourceCllDTOs)) {
            return;
        }

        for (KbRepoCollectionDTO sourceCllDTO : sourceCllDTOs) {
            FSSyncParams params = JsonUtils.parse(sourceCllDTO.getSyncParams(), FSSyncParams.class);
            if (params == null) {
                return;
            }
            if (!sourceCllDTO.getDeepImport()) {
                return;
            }
            FSAppConfig serverConfig = repoInfo.getFeishuServerConfig();
            NodeData nodeData = new NodeData();
            nodeData.setObjToken(params.getObjToken());
            nodeData.setDeepImport(sourceCllDTO.getDeepImport());

            // nodeToken、spaceId 查飞书当前节点信息
            Client client = clientService.getClient(serverConfig.getAppId());
            Node spaceNode = getSpaceNode(params.getObjToken(), client);
            if (spaceNode == null) {
                return;
            }
            nodeData.setNodeToken(spaceNode.getNodeToken());
            nodeData.setSpaceId(spaceNode.getSpaceId());
            nodeData.setObjType(FSFileType.getByCode(sourceCllDTO.getExternalFileType()));

            FSDocSyncCommand syncCommand = fsaAssembler.convertDSC(sourceCllDTO);
            syncCommand.setAppId(serverConfig.getAppId());
            syncCommand.setNodeData(Lists.newArrayList(nodeData));
            syncCommand.setTenantId(repoInfo.getTenantId());
            wikiFileService.importFSDocx(syncCommand, this.params.needUpdate);
        }
    }

    private void processColl(List<Long> collIds) {
        if (CollUtil.isEmpty(collIds)) {
            return;
        }
        List<KbRepoCollectionDTO> sourceCllDTOs = collectionRepository.selectCollectionByIds(collIds);
        if (CollUtil.isEmpty(sourceCllDTOs)) {
            return;
        }
        Map<Long, List<KbRepoCollectionDTO>> repoIdMap = sourceCllDTOs.stream().collect(Collectors.groupingBy(KbRepoCollectionDTO::getRepoId));
        repoIdMap.forEach((repoId, collDTO) -> {
            KbRepoBaseInfoPO repoInfo = repoBaseInfoRepository.getRepoById(repoId);
            try {
                GennRequestContext.initTenantId(repoInfo.getTenantId());
                processColl(repoInfo, collDTO);
            } catch (Exception e) {
                log.error("同步飞书文档异常 ", e);
            } finally {
                GennRequestContext.clear();
            }
        });

    }


    private Node getSpaceNode(String objToken, Client client) {
        GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder()
            .token(objToken)
            .objType("docx")
            .build();

        // 发起请求
        GetNodeSpaceResp resp = null;
        try {
            resp = client.wiki().v2().space().getNode(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            return null;
        }

        // 处理服务端错误
        assert resp != null;
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            return null;
        }
        return resp.getData().getNode();
    }
}
