package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.command.rtc.RTCPitfallConfirmCommand;
import cn.genn.ai.hub.app.application.command.rtc.RTCPitfallSubmitCommand;
import cn.genn.ai.hub.app.application.dto.AgentInfoDTO;
import cn.genn.ai.hub.app.application.dto.rtc.*;
import cn.genn.ai.hub.app.application.query.rtc.RTCPitfallPageQuery;
import cn.genn.ai.hub.app.application.service.action.RTCPitfallActionService;
import cn.genn.ai.hub.app.application.service.query.RTCPitfallQueryService;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.RTCProperties;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.AgentInfoRepositoryImpl;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "RTC")
@RestController
@RequestMapping("/rtc")
@RequiredArgsConstructor
public class RTCController {

    private final GennAIHubProperties gennAIHubProperties;
    private final AgentInfoRepositoryImpl agentInfoRepository;
    private final RTCPitfallActionService rtcPitfallActionService;
    private final RTCPitfallQueryService rtcPitfallQueryService;


    @PostMapping("/agentList")
    @Operation(summary = "获取rtc智能体列表")
    public List<RTCAgentATO> getAgentList() {
        List<RTCProperties.Agent> agentList = gennAIHubProperties.getRtc().getAgents().get(CurrentUserHolder.getTenantId());
        List<RTCAgentATO> agentDTOS = agentList.stream()
            .filter(agent -> !RTCAgentType.ANALYSIS.getCode().equals(agent.getType()))
            .map(agent -> RTCAgentATO.builder()
                .agentType(RTCAgentType.fromCode(agent.getType()))
                .appId(agent.getAppId())
                .build())
            .toList();
        // 设置智能体名称、描述和图标
        agentDTOS.forEach(agentDTO -> {
            AgentInfoDTO agentInfo = agentInfoRepository.getAgentByWorkflowId(agentDTO.getAppId());
            if (agentInfo != null) {
                agentDTO.setName(agentInfo.getName());
                agentDTO.setDescription(agentInfo.getDescription());
                agentDTO.setAvatar(agentInfo.getAvatar());
            }
        });
        return agentDTOS;
    }

    /**
     * 分页查询列表
     *
     * @param query 查询条件
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询列表")
    public PageResultDTO<RTCPitfallPageDTO> page(@Parameter(description = "查询类") @RequestBody RTCPitfallPageQuery query) {
        return rtcPitfallQueryService.page(query);
    }

    /**
     * 详情
     *
     * @param query 查询条件
     */
    @PostMapping("/get")
    @Operation(summary = "详情")
    public RTCPitfallDetailDTO get(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return rtcPitfallQueryService.get(query);
    }

    @PostMapping("/submit")
    @Operation(summary = "隐患上报")
    public RTCPitfallInfoDTO submit(@Valid @RequestBody RTCPitfallSubmitCommand command) {
        return rtcPitfallActionService.submit(command);
    }

    @PostMapping("/confirm")
    @Operation(summary = "隐患确认")
    public RTCPitfallInfoDTO confirm(@Valid @RequestBody RTCPitfallConfirmCommand command) {
        return rtcPitfallActionService.confirm(command);
    }

    @PostMapping("/resolve")
    @Operation(summary = "隐患解决")
    public RTCPitfallInfoDTO resolve(@Valid @RequestBody RTCPitfallConfirmCommand command) {
        return rtcPitfallActionService.resolve(command);
    }

    @PostMapping("/getSevenAnalysis")
    @Operation(summary = "查询隐患7日分析")
    public RTCPitfallAnalysisDTO getSevenAnalysis() {
        return rtcPitfallQueryService.getSevenAnalysis();
    }

    @PostMapping("/pitfallAnalysis")
    @Operation(summary = "隐患7日分析")
    public RTCPitfallAnalysisDTO pitfallAnalysis() {
        return rtcPitfallActionService.pitfallAnalysis();
    }
}
