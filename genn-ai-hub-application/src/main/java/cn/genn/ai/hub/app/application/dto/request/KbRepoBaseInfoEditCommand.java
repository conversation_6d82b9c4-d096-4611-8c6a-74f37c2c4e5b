package cn.genn.ai.hub.app.application.dto.request;

import cn.genn.ai.hub.app.application.dto.feishu.FSAppConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 知识库目录结构
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoBaseInfoEditCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @NotEmpty(message = "知识库ID不能为空")
    private Long id;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "团队id")
    private Long teamId;

    @Schema(description = "知识库图标，支持自定义图标 URL")
    private String avatar;

    @Schema(description = "简介，知识库的功能描述")
    private String description;

    @Schema(description = "向量模型--对应模型管理的name")
    private String vectorModelKey;
//
    @Schema(description = "代理模型key，用于 QA 生成的 AI 模型名称")
    private String agentModelKey;

    @Schema(description = "图片识别模型名称--对应模型管理的name")
    private String imgModelKey;

//    @Schema(description = "知识库类型, repo:REPO-通用知识库, website:WEBSITE-网址, feishu:FEISHU-飞书,yuque:YUQUE-语雀")
//    private RepoTypeEnum repoType;
//
//    @Schema(description = "状态, active:ACTIVE-激活, syncing:SYNCING-同步中, error:ERROR-错误")
//    private RepoStatusEnum repoStatus;
//
//    @Schema(description = "网站配置（仅网站型知识库有效），包含 url, selector")
//    private String websiteConfig;
//
//    @Schema(description = "API 文件服务器配置，用于第三方 API 文件接入")
//    private String apiServerConfig;
//
    @Schema(description = "飞书服务器配置，包含飞书应用凭证和同步参数")
    private FSAppConfig feishuServerConfig;
//
//    @Schema(description = "自动同步开关，控制是否定时同步外部数据源（默认 false）")
//    private Byte autoSync;

}

