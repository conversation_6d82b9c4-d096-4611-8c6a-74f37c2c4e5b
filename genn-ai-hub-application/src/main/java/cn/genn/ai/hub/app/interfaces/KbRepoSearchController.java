package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.api.KbAbilityApiImpl;
import cn.genn.ai.hub.app.application.command.KbRepoSearchCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoRealtimeFileParseBody;
import cn.genn.ai.hub.app.application.enums.ModelRequestSource;
import cn.genn.ai.hub.app.application.service.action.KbRepoSearchActionService;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.core.api.kb.KbSearchRequest;
import cn.genn.ai.hub.core.api.kb.KbSearchResponse;
import cn.genn.ai.hub.plugin.common.ability.QuestionOptimizationAbility;
import cn.genn.ai.hub.plugin.impl.kb.model.QuestionOptimizationParam;
import cn.genn.spring.boot.starter.ai.component.log.RequestModelLog;
import cn.genn.spring.boot.starter.ai.component.log.RequestModelLogHolder;
import cn.genn.spring.boot.starter.ai.component.log.RequestModelLogInfo;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.genn.spring.boot.starter.upm.model.SsoUserAuthInfoDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 知识库数据检索
 * @Date: 2025/3/18
 * @Author: kangjian
 */
@Tag(name = "知识库数据检索")
@RestController
@RequestMapping("/kbRepoSearch")
@RequiredArgsConstructor
public class KbRepoSearchController {

    private final KbRepoSearchActionService searchActionService;
    private final FileStorageOperation fileStorageOperation;
    private final QuestionOptimizationAbility questionOptimizationAbility;
    private final KbAbilityApiImpl kbAbilityApi;

    /**
     * 按知识库范围进行检索
     * todo 权限待确认
     *
     * @param command command
     * @return KbSearchResponse
     */
    @PostMapping("/searchOfSingleRepo")
    @Operation(summary = "按单个知识库范围进行检索")
    @RequestModelLog
    public KbSearchResponse searchOfSingleRepo(@RequestBody KbRepoSearchCommand command) {
        List<String> questions = ListUtil.toList(command.getQuestion());
        fillModeLog(command);
        QuestionOptimizationParam optimizationParam = command.getOptimizationParam();
        if (Objects.equals(true, optimizationParam.getEnabled())) {
            //1. 问题优化
            List<String> optimize = questionOptimizationAbility.optimize(null, optimizationParam.getHistorySize(),
                command.getQuestion(), optimizationParam.getOptimizationModel(), optimizationParam.getOptimizationPrompt(),
                Objects.equals(true, command.getOptimizationParam().getEnableTerm()) ? kbAbilityApi.listTerms(command.getRepoIds()) : null);
            if (CollUtil.isNotEmpty(optimize)) {
                questions.addAll(optimize);
            }
        }

        KbSearchRequest request = KbSearchRequest
            .builder()
            .repoIds(command.getRepoIds())
            .searchMode(command.getSearchMode())
            .questions(questions)
            .needRerank(command.getNeedRerank())
            .needQaPairIntercept(command.getNeedQaPairIntercept())
            .needCasesIntercept(command.getNeedCasesIntercept())
            .topK(command.getTopK())
            .score(command.getScore())
            .build();

        KbSearchResponse searchResponse = searchActionService.searchOfSingleRepo(request);
        searchResponse.setRequestParams(command);
        return searchResponse;
    }
    private void fillModeLog(KbRepoSearchCommand command) {
        RequestModelLogInfo requestModelLog = RequestModelLogHolder.get();
        if (CurrentUserHolder.CURRENT_USER_THREAD_LOCAL.get() != null) {
            requestModelLog.setUserId(CurrentUserHolder.getUserId().toString());
            requestModelLog.setUserName(CurrentUserHolder.getUserName());
        }

        requestModelLog.setRequestSource(ModelRequestSource.KNOWLEDGE.getCode());
        requestModelLog.setRequestKey(command.getRepoIds().getFirst().toString());
        RequestModelLogHolder.set(requestModelLog);
    }

    /**
     * 实时解析文件内容
     */
    @PostMapping("/realtimeFileParse")
    @Operation(summary = "实时解析文件内容")
    public String realtimeFileParse(@RequestBody KbRepoFileDTO dto) {
        // 生成一个外链
        String preSignedUrl = fileStorageOperation.preSignedUrlGetObject(dto.getExternalFileId(), null);
        dto.setExternalFileUrl(preSignedUrl);
        return searchActionService.realtimeFileParse(dto.getExternalFileId(), dto.getExternalFileUrl(), dto.getFileName(), dto.getContentType());
    }

    /**
     * 实时解析文件内容并分块
     */
    @PostMapping("/realtimeFileParseChunk")
    @Operation(summary = "实时解析文件内容并分块")
    public List<KbRepoRealtimeFileParseBody> realtimeFileParseChunk(@RequestBody KbRepoFileDTO dto) {
        // 生成一个外链
        String preSignedUrl = fileStorageOperation.preSignedUrlGetObject(dto.getExternalFileId(), null);
        dto.setExternalFileUrl(preSignedUrl);
        return searchActionService.realtimeFileParseChunk(dto.getExternalFileId(), dto.getExternalFileUrl(), dto.getFileName(), dto.getContentType());
    }
}
