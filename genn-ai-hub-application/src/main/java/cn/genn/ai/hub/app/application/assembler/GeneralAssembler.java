package cn.genn.ai.hub.app.application.assembler;

import cn.genn.ai.hub.app.application.command.KbRepoCasesCommand;
import cn.genn.ai.hub.app.application.command.QuestionGapAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.feishu.AvatarDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionInfoDTO;
import cn.genn.ai.hub.core.api.kb.CasesInfo;
import cn.genn.ai.hub.core.api.kb.KbCreateCasesRequest;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.lark.oapi.service.contact.v3.model.AvatarInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GeneralAssembler {

    GeneralAssembler INSTANCE = Mappers.getMapper(GeneralAssembler.class);

    AvatarDTO convertFSAvatar(AvatarInfo avatar);

    @Mapping(target = "questionId", source = "id")
    QuestionGapAnalysisCommand convertGapAnl(QuestionInfoDTO dto);

    KbRepoCasesCommand convertCases(CasesInfo casesInfo);

    default List<KbRepoCasesCommand> convertCasesCommand(KbCreateCasesRequest request) {
        List<KbRepoCasesCommand> result = Lists.newArrayList();
        if (request == null || request.getRepoId() == null || CollUtil.isEmpty(request.getCasesInfos())) {
            return result;
        }
        for (CasesInfo casesInfo : request.getCasesInfos()) {
            KbRepoCasesCommand kbRepoCasesCommand = convertCases(casesInfo);
            kbRepoCasesCommand.setRepoId(request.getRepoId());
            result.add(kbRepoCasesCommand);
        }
        return result;
    }
}

