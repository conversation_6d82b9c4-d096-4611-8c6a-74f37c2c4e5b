package cn.genn.ai.hub.app.application.command;

import cn.genn.ai.hub.core.api.kb.SearchFilterParam;
import cn.genn.ai.hub.core.api.kb.SearchModeEnum;
import cn.genn.ai.hub.plugin.impl.kb.model.QuestionOptimizationParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 知识库查询请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KbRepoSearchCommand {


    @Schema(description = "知识库ID")
    private List<Long> repoIds;

    /**
     * 检索模式，默认为语义检索
     */
    @Builder.Default
    @Schema(description = "检索模式，默认为语义检索")
    private SearchModeEnum searchMode = SearchModeEnum.SEMANTIC;

    /**
     * 用户问题
     */
    @Schema(description = "用户问题")
    private String question;

    /**
     * 是否重排序，对检索结果进行重新排序
     */
    @Builder.Default
    @Schema(description = "是否重排序，对检索结果进行重新排序")
    private Boolean needRerank = false;

    /**
     * 是否开启标准问答对拦截
     */
    @Builder.Default
    @Schema(description = "是否开启标准问答对拦截")
    private Boolean needQaPairIntercept = false;

    /**
     * 是否开启案例库拦截
     */
    @Builder.Default
    @Schema(description = "是否开启案例库拦截")
    private Boolean needCasesIntercept = false;

    @Builder.Default
    @Schema(description = "topK")
    private Integer topK = 10;

    /**
     * 分数 0.0-1.0
     */
    @Builder.Default
    @Schema(description = "分数 0.0-1.0")
    private Double score = 0.5;

    /**
     * 问题优化参数，包含是否启用，优化模型，优化提示词等
     */
    @Builder.Default
    private QuestionOptimizationParam optimizationParam = QuestionOptimizationParam.builder().build();
}
