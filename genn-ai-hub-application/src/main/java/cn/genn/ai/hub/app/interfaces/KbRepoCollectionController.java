package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.dto.KbCatFullPath;
import cn.genn.ai.hub.app.application.dto.KbRepoAfreshTrainCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoCollectionDTO;
import cn.genn.ai.hub.app.application.dto.LocalSheetContentResp;
import cn.genn.ai.hub.app.application.dto.feishu.ExistFileDTO;
import cn.genn.ai.hub.app.application.dto.request.*;
import cn.genn.ai.hub.app.application.enums.SubjectType;
import cn.genn.ai.hub.app.application.service.action.KbRepoCollectionActionService;
import cn.genn.ai.hub.app.application.service.query.KbRepoCollectionQueryService;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.config.auth.Auth;
import cn.genn.ai.hub.app.infrastructure.config.auth.BatchOrAuth;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.kb.KbRepoCollectionAuthParamResolver;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team.KbCollectionTeamManagerResourceAuthParamResolver;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识库的数据集
 * <AUTHOR>
 */
@Tag(name = "知识库的数据集")
@RestController
@RequestMapping("/kbRepoCollection")
@RequiredArgsConstructor
public class KbRepoCollectionController {

    private final KbRepoCollectionQueryService queryService;
    private final KbRepoCollectionActionService actionService;


    /**
     * 查询已导入的飞书文件token列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/fs/exist/file")
    @Operation(summary = "查询已导入的飞书文件token列表")
    public List<ExistFileDTO> alreadyExist(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return queryService.alreadyExist(query.getId());
    }


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询数据集列表")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.VIEW, ActionType.EDIT}, fieldKey = "#query.repoId"),
        @Auth(uniqueId = "pageCollection", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER, ActionType.VIEW}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public PageResultDTO<KbRepoCollectionDTO> page(@Parameter(description = "查询类") @RequestBody KbRepoCollectionQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return
     */
    @PostMapping("/get")
    @Operation(summary = "根据id查询")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.VIEW, ActionType.EDIT}, paramResolver = KbRepoCollectionAuthParamResolver.class),
        @Auth(uniqueId = "getCollection", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER, ActionType.VIEW}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public KbRepoCollectionDTO get(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return queryService.get(query.getId());
    }

    /**
     * 根据知识库查询下面的所有的数据集
     */
    @PostMapping("/getKbRepoCollectionList")
    @Operation(summary = "根据知识库查询下面的所有的数据集")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.VIEW, ActionType.EDIT}, fieldKey = "#query.repoId"),
        @Auth(uniqueId = "getCollectionList", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER, ActionType.VIEW}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public PageResultDTO<KbRepoCollectionDTO> getKbRepoCollectionList(@RequestBody @Valid KbRepoCollectionListQuery query) {
        return queryService.getKbRepoCollectionList(query);
    }

    /**
     * 移动数据集
     */
    @PostMapping("/move")
    @Operation(summary = "移动数据集")
    @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, paramResolver = KbRepoCollectionAuthParamResolver.class)
    @Deprecated
    public Boolean move(@RequestBody KbRepoCollectionMoveCommand command) {
        return actionService.move(command);
    }

    /**
     * 重命名数据集
     */
    @PostMapping("/rename")
    @Operation(summary = "重命名数据集")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, paramResolver = KbRepoCollectionAuthParamResolver.class),
        @Auth(uniqueId = "renameCollection", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Boolean rename(@RequestBody KbRepoCollectionRenameCommand command) {
        return actionService.rename(command);
    }

    /**
     * 删除数据集
     */
    @PostMapping("/delete")
    @Operation(summary = "删除数据集")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER}, paramResolver = KbRepoCollectionAuthParamResolver.class),
        @Auth(uniqueId = "deleteCollection", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Boolean delete(@RequestBody IdCommand command) {
        return actionService.deleteById(command.getId());
    }

    /**
     * 更新数据集启用状态
     */
    @PostMapping("/updateStatus")
    @Operation(summary = "更新数据集启用状态")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, paramResolver = KbRepoCollectionAuthParamResolver.class),
        @Auth(uniqueId = "updateCollectionStatus", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Boolean updateStatus(@RequestBody KbRepoCollectionUpdateStatusCommand command) {
        return actionService.updateStatus(command);
    }

    /**
     * 新建文件夹
     */
    @PostMapping("/createFolder")
    @Operation(summary = "新建知识库数据集的文件夹")
    @Deprecated
    public Long createFolder(@RequestBody KbRepoCollectionFolderCreateCommand command) {
        return actionService.createFolder(command);
    }

    /**
     * 新建手动数据集 没有分段参数 分块由自己录入
     */
    @PostMapping("/createManualCollection")
    @Operation(summary = "新建手动数据集")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "createManualCollection", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long createManualCollection(@Valid @RequestBody KbRepoCollectionManualCreateCommand command) {
        return actionService.createManualCollection(command);
    }

    /**
     * 新建文本数据集 本地上传
     */
    @PostMapping("/createTextCollectionOfFile")
    @Operation(summary = "新建文本数据集 本地上传")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "createTextCollectionOfFile", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long createTextCollection(@Valid @RequestBody KbRepoCollectionTextOfFileCreateCommand command) {
        command.setTenantId(CurrentUserHolder.getTenantId());
        return actionService.createTextCollectionOfFile(command);
    }

    /**
     * 新建表格数据集
     */
    @PostMapping("/create/sheet")
    @Operation(summary = "新建表格数据集")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "createSheetCollection", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long createSheetColl(@Valid @RequestBody KbRepoCollectionSheetCreateCommand command) {
        return actionService.createSheetColl(command);
    }

    /**
     * 新建表格数据集
     */
    @PostMapping("/sheet/content")
    @Operation(summary = "表格sheet数据")
    public List<LocalSheetContentResp> sheetContent(@Valid @RequestBody KbSheetContentCommand command) {
        return actionService.sheetContent(command);
    }

    /**
     * 重新训练
     */
    @PostMapping("/afresh/train")
    @Operation(summary = "重新训练")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "afreshTrain", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long afreshTrain(@Valid @RequestBody KbRepoAfreshTrainCommand command) {
        command.setTenantId(CurrentUserHolder.getTenantId());
        return actionService.afreshTrain(command);
    }

    /**
     * 新建文本数据集 网页链接
     */
    @PostMapping("/createTextCollectionOfUrl")
    @Operation(summary = "新建文本数据集 网页链接")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "createTextCollectionOfUrl", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long createTextCollection(@Valid @RequestBody KbRepoCollectionTextOfUrlCreateCommand command) {
        return actionService.createTextCollectionOfUrl(command);
    }

    /**
     * 新建文本数据集 自定义文本
     */
    @PostMapping("/createTextCollectionOfText")
    @Operation(summary = "新建文本数据集 自定义文本")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "createTextCollectionOfText", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long createTextCollection(@Valid @RequestBody KbRepoCollectionTextOfTextCreateCommand command) {
        command.setTenantId(CurrentUserHolder.getTenantId());
        return actionService.createTextCollectionOfText(command);
    }

    /**
     * 查询当前路径
     *
     * @param query
     * @return
     */
    @PostMapping("/full/path")
    @Operation(summary = "查询当前路径")
    public List<KbCatFullPath> fullPath(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return queryService.fullPath(query);
    }
}

