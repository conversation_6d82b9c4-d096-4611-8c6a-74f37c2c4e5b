package cn.genn.ai.hub.app.domain.question.service;

import cn.genn.ai.hub.app.application.command.QuestionAnalysisCommand;
import cn.genn.ai.hub.app.application.dto.question.DepartmentQuestionCountDTO;
import cn.genn.ai.hub.app.application.dto.question.DepartmentUserDTO;
import cn.genn.ai.hub.app.application.dto.question.QuestionAnalysisDTO;
import cn.genn.ai.hub.app.application.query.DepartmentQuestionCountQuery;
import cn.genn.ai.hub.app.application.query.QuestionAnalysisQuery;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.QuestionAnalysisRepositoryImpl;
import cn.genn.ai.hub.app.application.assembler.QuestionInfoAssembler;
import cn.genn.spring.boot.starter.upm.component.IUpmUserService;
import cn.genn.spring.boot.starter.upm.model.UpmUserDTO;
import cn.genn.spring.boot.starter.upm.model.UpmUserQuery;
import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 应用查询服务,查询可自由调用下层,外部服务等,不受限制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QuestionAnalysisService {

    private final QuestionAnalysisRepositoryImpl analysisRepository;
    private final QuestionInfoAssembler questionInfoAssembler;
    private final IUpmUserService upmUserService;

    /**
     * 查询列表
     *
     * @param query 查询条件
     * @return QuestionAnalysisDTO
     */
    public List<QuestionAnalysisDTO> analysisDTOs(QuestionAnalysisQuery query) {
        return analysisRepository.analysisDTOs(query);
    }


    @Transactional
    public void addBatch(List<QuestionAnalysisCommand> commands) {
        analysisRepository.addBatch(commands);
    }
}

