package cn.genn.ai.hub.app.application.dto;

import cn.genn.ai.hub.app.application.dto.feishu.FSAppConfig;
import cn.genn.ai.hub.app.application.enums.RepoStatusEnum;
import cn.genn.ai.hub.app.application.enums.RepoTypeEnum;
import cn.genn.ai.hub.app.application.enums.KbRepoSourceEnum;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * KbRepoBaseInfoDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoBaseInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "目录id")
    private Long tenantId;

    @Schema(description = "目录id")
    private Long catId;

    @Schema(description = "团队id")
    private Long teamId;

    @Schema(description = "标签列表")
    private List<TagsSimpleDTO> tagList;

    @Schema(description = "目录名称路径")
    private List<String> catNamePath;

    @Schema(description = "目录id路径")
    private List<Long> catIdPath;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "知识库图标，支持自定义图标 URL")
    private String avatar;

    @Schema(description = "向量模型--对应模型管理的name")
    private String vectorModelKey;

    @Schema(description = "文本识别模型--对应模型管理的name")
    private String agentModelKey;

    @Schema(description = "图片识别模型名称--对应模型管理的name")
    private String imgModelKey;

    @Schema(description = "简介，知识库的功能描述")
    private String description;

    @Schema(description = "协作者")
    private List<CollaboratorDTO> collaborators;

    @Schema(description = "知识库类型, repo:REPO-通用知识库, website:WEBSITE-网址, feishu:FEISHU-飞书,yuque:YUQUE-语雀")
    private RepoTypeEnum repoType;

    @Schema(description = "状态, active:ACTIVE-激活, syncing:SYNCING-同步中, error:ERROR-错误")
    private RepoStatusEnum repoStatus;

    @Schema(description = "应用渠道ID")
    private Long channelId;

    @Schema(description = "应用渠道ID")
    private Long channelRefId;

    @Schema(description = "网站配置（仅网站型知识库有效），包含 url, selector")
    private String websiteConfig;

    @Schema(description = "API 文件服务器配置，用于第三方 API 文件接入")
    private String apiServerConfig;

    @Schema(description = "飞书服务器配置，包含飞书应用凭证和同步参数")
    private FSAppConfig feishuServerConfig;

    @Schema(description = "自动同步开关，控制是否定时同步外部数据源（默认 false）")
    private Boolean autoSync;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "创建者名称")
    private String createUserName;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "修改人")
    private Long updateUserId;

    @Schema(description = "修改人名称")
    private String updateUserName;

    @Schema(description = "逻辑删除（0：未删除  1：删除）")
    private DeletedTypeEnum deleted;

    @Schema(description = "知识库权限")
    private List<ActionType> authActions;

    @Schema(description = "知识库来源类型")
    private KbRepoSourceEnum source;


}

