package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.KbRepoBaseInfoAssembler;
import cn.genn.ai.hub.app.application.dto.KbRepoBaseInfoDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoCreateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoBaseInfoEditCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCatDetailQuery;
import cn.genn.ai.hub.app.application.enums.RepoTypeEnum;
import cn.genn.ai.hub.app.application.enums.KbRepoSourceEnum;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoBaseInfoMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCategoryMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCategoryPO;
import cn.genn.cache.redis.annotation.Cache;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.genn.ai.hub.app.infrastructure.constant.CacheConstants.AGENT_TENANT_ID_CACHE;

/**
 * @Date: 2025/3/7
 * @Author: kangjian
 */
@Repository
public class KbRepoBaseInfoRepositoryImpl extends ServiceImpl<KbRepoBaseInfoMapper, KbRepoBaseInfoPO> {

    @Resource
    private KbRepoBaseInfoMapper mapper;
    @Resource
    private KbRepoBaseInfoAssembler assembler;
    @Resource
    private KbRepoCategoryMapper categoryMapper;

    /**
     * 获取知识库目录详情 补充path路径
     *
     * @param query
     * @return
     */
    public List<KbRepoBaseInfoDTO> getKbRepoCatDetail(KbRepoCatDetailQuery query) {
        QueryWrapper<KbRepoBaseInfoPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoBaseInfoPO::getCatId, query.getCatId())
            .eq(Objects.nonNull(query.getRepoType()), KbRepoBaseInfoPO::getRepoType, query.getRepoType())
            .like(StrUtil.isNotBlank(query.getName()), KbRepoBaseInfoPO::getName, query.getName())
            .eq(KbRepoBaseInfoPO::getDeleted, DeletedEnum.NOT_DELETED);
        List<KbRepoBaseInfoDTO> dtoList = assembler.PO2DTO(mapper.selectList(queryWrapper));
        if (CollUtil.isEmpty(dtoList)) {
            return dtoList;
        }
        // 如果存在父目录,需要赋值path路径的信息
        Set<Long> parentIds = dtoList.stream().map(KbRepoBaseInfoDTO::getCatId).collect(Collectors.toSet());
        parentIds.remove(0L);
        if (CollUtil.isNotEmpty(parentIds)) {
            // 查询全部目录 构造
            QueryWrapper<KbRepoCategoryPO> queryParentWrapper = new QueryWrapper<>();
            queryParentWrapper.lambda()
                .eq(KbRepoCategoryPO::getDeleted, DeletedEnum.NOT_DELETED);
            List<KbRepoCategoryPO> allCategoryPOs = categoryMapper.selectList(queryParentWrapper);
            Map<Long, KbRepoCategoryPO> allCategoryPOsMap = allCategoryPOs.stream().collect(Collectors.toMap(KbRepoCategoryPO::getId, Function.identity()));
            for (KbRepoBaseInfoDTO dto : dtoList) {
                List<Long> catIdPath = new ArrayList<>();
                List<String> catNamePath = new ArrayList<>();
                Long currentPid = dto.getCatId();
                while (currentPid != null && currentPid != 0L) {
                    KbRepoCategoryPO parentPO = allCategoryPOsMap.get(currentPid);
                    if (parentPO != null) {
                        catIdPath.add(0, parentPO.getId());
                        catNamePath.add(0, parentPO.getCatName());
                        currentPid = parentPO.getPid();
                    } else {
                        break;
                    }
                }
                dto.setCatIdPath(catIdPath);
                dto.setCatNamePath(catNamePath);
            }
        }
        return dtoList;
    }

    public List<KbRepoBaseInfoDTO> getByCateId(Long cateId) {
        QueryWrapper<KbRepoBaseInfoPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoBaseInfoPO::getCatId, cateId)
            .eq(KbRepoBaseInfoPO::getDeleted, DeletedEnum.NOT_DELETED);
        return assembler.PO2DTO(mapper.selectList(queryWrapper));
    }

    public Boolean editKbRepoDetail(KbRepoBaseInfoEditCommand command) {
        KbRepoBaseInfoPO baseInfo = assembler.convertPO(command);
        return updateById(baseInfo);
    }

    public Boolean moveRepo(Long curId, Long tagCatId) {
        UpdateWrapper<KbRepoBaseInfoPO> update = new UpdateWrapper<>();
        update.lambda()
            .eq(KbRepoBaseInfoPO::getId, curId)
            .set(KbRepoBaseInfoPO::getCatId, tagCatId);
        return update(update);
    }

    public Long createKbRepo(KbRepoBaseInfoCreateCommand command) {
        KbRepoBaseInfoPO baseInfo = assembler.convertPO(command);
        mapper.insert(baseInfo);
        return baseInfo.getId();
    }

    public Boolean deleteById(Long id) {
        UpdateWrapper<KbRepoBaseInfoPO> update = new UpdateWrapper<>();
        update.lambda()
            .eq(KbRepoBaseInfoPO::getId, id)
            .set(KbRepoBaseInfoPO::getDeleted, DeletedTypeEnum.DELETED);
        return update(update);
    }

    @Cache(value = AGENT_TENANT_ID_CACHE, fieldKey = "#kbId", expireTime = 0)
    @IgnoreTenant
    public Long getTenantIdByKnowledgeId(Long kbId) {
        KbRepoBaseInfoPO kbRepoBaseInfoPO = mapper.selectOne(new LambdaQueryWrapper<KbRepoBaseInfoPO>()
            .eq(KbRepoBaseInfoPO::getId, kbId));
        if (kbRepoBaseInfoPO == null) {
            return 0L;
        }
        return kbRepoBaseInfoPO.getTenantId();
    }

    @IgnoreTenant
    public KbRepoBaseInfoPO getRepoById(Long repoId) {
        return getById(repoId);
    }

    @IgnoreTenant
    public List<KbRepoBaseInfoPO> getFSRepoInfos(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        QueryWrapper<KbRepoBaseInfoPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .in(CollUtil.isNotEmpty(ids), KbRepoBaseInfoPO::getId, ids)
            .eq(KbRepoBaseInfoPO::getRepoType, RepoTypeEnum.FEISHU)
            .eq(KbRepoBaseInfoPO::getDeleted, DeletedEnum.NOT_DELETED);
        return mapper.selectList(queryWrapper);
    }

    /**
     * 根据用户ID查询gewu知识库
     */
    public KbRepoBaseInfoPO getGewuKnowledgeBaseByUserId(Long userId) {
        LambdaQueryWrapper<KbRepoBaseInfoPO> queryWrapper = new QueryWrapper<KbRepoBaseInfoPO>().lambda();
        queryWrapper.eq(KbRepoBaseInfoPO::getCreateUserId, userId)
            .eq(KbRepoBaseInfoPO::getSource, KbRepoSourceEnum.GEWU)
            .eq(KbRepoBaseInfoPO::getDeleted, DeletedTypeEnum.NOT_DELETED);

        return mapper.selectOne(queryWrapper);
    }
}
