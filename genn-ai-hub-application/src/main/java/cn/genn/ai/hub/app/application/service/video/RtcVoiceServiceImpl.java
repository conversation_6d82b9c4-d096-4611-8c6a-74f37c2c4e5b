package cn.genn.ai.hub.app.application.service.video;

import cn.genn.ai.hub.app.application.assembler.FSAssembler;
import cn.genn.ai.hub.app.application.dto.feishu.video.PostVoiceChatBean;
import cn.genn.ai.hub.app.application.dto.feishu.video.ResponseBean;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.core.utils.jackson.JsonUtils;
import com.alibaba.fastjson.JSON;
import com.volcengine.error.SdkError;
import com.volcengine.model.response.RawResponse;
import com.volcengine.service.BaseServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;


/**
 * 音视频聊天
 *
 * <AUTHOR>
 */
@Slf4j
public class RtcVoiceServiceImpl extends BaseServiceImpl implements RtcVoiceService {

    @Resource
    private FSAssembler fsaAssembler;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private GennAIHubProperties aiHubProperties;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    private RtcVoiceServiceImpl() {
        super(RtcVoiceConfig.serviceInfo, RtcVoiceConfig.apiInfoList);
    }

    private static final RtcVoiceServiceImpl videoService = new RtcVoiceServiceImpl();

    public static RtcVoiceServiceImpl getInstance() {
        return videoService;
    }

    @Override
    public ResponseBean startVoiceChat(PostVoiceChatBean postVoiceChatBean) throws Exception {
        String body = JsonUtils.toJson(postVoiceChatBean);
        log.info("postAIBot startVoiceChat request: {}", body);
        RawResponse response = json("StartVoiceChat", null, body);
        if (response.getCode() != SdkError.SUCCESS.getNumber()) {
            log.error("postAIBot startVoiceChat error response: ", response.getException());
            throw response.getException();
        }
        log.info("postAIBot startVoiceChat response: {}  {}", response.getCode(), response.getData());
        return JSON.parseObject(response.getData(), ResponseBean.class);

    }

    @Override
    public ResponseBean stopVoiceChat(PostVoiceChatBean postVoiceChatBean) throws Exception {
        RawResponse response = json("StopVoiceChat", null, JsonUtils.toJson(postVoiceChatBean));
        if (response.getCode() != SdkError.SUCCESS.getNumber()) {
            log.error("postAIBot StopVoiceChat error response: ", response.getException());
            throw response.getException();
        }
        return JSON.parseObject(response.getData(), ResponseBean.class);
    }

    @Override
    public ResponseBean updateVoiceChat(PostVoiceChatBean postVoiceChatBean) throws Exception {
        String body = JsonUtils.toJson(postVoiceChatBean);
        log.info("postAIBot UpdateVoiceChat request: {}", body);
        RawResponse response = json("UpdateVoiceChat", null, body);
        if (response.getCode() != SdkError.SUCCESS.getNumber()) {
            log.error("postAIBot UpdateVoiceChat error response: ", response.getException());
            throw response.getException();
        }
        return JSON.parseObject(response.getData(), ResponseBean.class);
    }

}

