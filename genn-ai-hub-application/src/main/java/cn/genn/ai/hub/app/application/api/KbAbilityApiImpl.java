package cn.genn.ai.hub.app.application.api;

import cn.genn.ai.hub.app.application.assembler.GeneralAssembler;
import cn.genn.ai.hub.app.application.command.KbRepoCasesCommand;
import cn.genn.ai.hub.app.application.service.KbRepoCasesService;
import cn.genn.ai.hub.app.application.service.action.KbRepoSearchActionService;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoTermMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoTermPO;
import cn.genn.ai.hub.core.api.kb.*;
import cn.genn.core.model.enums.DeletedTypeEnum;
import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class KbAbilityApiImpl implements KbAbilityApi {

    @Resource
    private KbRepoSearchActionService searchActionService;
    @Resource
    private KbRepoTermMapper repoTermMapper;

    @Resource
    private KbRepoCasesService casesService;

    @Resource
    private GeneralAssembler generalAssembler;


    @Override
    @IgnoreTenant
    public KbSearchResponse search(KbSearchRequest request) {
        return searchActionService.searchOfSingleRepo(request);
    }

    @Override
    public Boolean batchCreateCases(KbCreateCasesRequest request) {
        List<KbRepoCasesCommand> commands = generalAssembler.convertCasesCommand(request);
        return casesService.batchCreate(commands);
    }

    /**
     * 实时解析文件返回原文信息
     *
     * @param externalFileId
     * @param externalFileUrl
     * @param fileName
     * @param contentType
     * @return 文件内容
     */
    @Override
    public String parseFile(String externalFileId, String externalFileUrl, String fileName, String contentType) {
        return searchActionService.realtimeFileParse(externalFileId, externalFileUrl, fileName, contentType);
    }

    @Override
    @IgnoreTenant
    public List<Term> listTerms(List<Long> repoIds) {
        List<KbRepoTermPO> kbRepoTermPOS = repoTermMapper.selectList(Wrappers.<KbRepoTermPO>lambdaQuery()
            .in(KbRepoTermPO::getRepoId, repoIds)
            .eq(KbRepoTermPO::getEnabled, true)
            .eq(KbRepoTermPO::getDeleted, DeletedTypeEnum.NOT_DELETED));
        if (CollUtil.isEmpty(kbRepoTermPOS)) {
            return Collections.emptyList();
        }
        return kbRepoTermPOS.stream().map(term -> Term.builder()
            .name(term.getName())
            .nameAliases(term.getNameAliases())
            .description(term.getDescription())
            .build()).toList();
    }


}
