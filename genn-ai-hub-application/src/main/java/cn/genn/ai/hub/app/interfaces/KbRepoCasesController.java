package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.command.IdListCommand;
import cn.genn.ai.hub.app.application.command.KbRepoCasesCommand;
import cn.genn.ai.hub.app.application.command.KbRepoCasesUploadCommand;
import cn.genn.ai.hub.app.application.dto.KbRepoCasesDTO;
import cn.genn.ai.hub.app.application.query.KbRepoCasesQuery;
import cn.genn.ai.hub.app.application.service.KbRepoCasesService;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.config.auth.Auth;
import cn.genn.ai.hub.app.infrastructure.config.auth.BatchOrAuth;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.kb.KbRepoQaAuthParamResolver;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team.KbCollectionTeamManagerResourceAuthParamResolver;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team.KbQATeamManagerResourceAuthParamResolver;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 案例库
 * <AUTHOR>
 */
@Tag(name = "案例库")
@RestController
@RequestMapping("/kbRepoCases")
@RequiredArgsConstructor
public class KbRepoCasesController {

    private final KbRepoCasesService casesService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "分页列表")
    public PageResultDTO<KbRepoCasesDTO> page(@Parameter(description = "查询类") @RequestBody KbRepoCasesQuery query) {
        return casesService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return
     */
    @PostMapping("/get")
    @Operation(summary = "根据id查询")
    public KbRepoCasesDTO get(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return casesService.get(query);
    }


    /**
     * 新建案例
     */
    @PostMapping("/create")
    @Operation(summary = "新建案例")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "createCases", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long create(@Valid @RequestBody KbRepoCasesCommand command) {
        return casesService.create(command);
    }

    /**
     * 批量新建案例
     */
    @PostMapping("/batchCreate")
    @Operation(summary = "批量新建案例")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "createCases", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public void batchCreate(@Valid @RequestBody List<KbRepoCasesCommand> commands) {
        casesService.batchCreate(commands);
    }

    /**
     * 编辑案例
     */
    @PostMapping("/edit")
    @Operation(summary = "编辑案例")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "editQA", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbQATeamManagerResourceAuthParamResolver.class)
    })
    public Boolean edit(@Valid @RequestBody KbRepoCasesCommand command) {
        return casesService.edit(command);
    }

    /**
     * 删除案例
     */
    @PostMapping("/delete")
    @Operation(summary = "删除案例")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, paramResolver = KbRepoQaAuthParamResolver.class),
        @Auth(uniqueId = "deleteQA", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbQATeamManagerResourceAuthParamResolver.class)
    })
    public Boolean delete(@Valid @RequestBody IdCommand command) {
        return casesService.delete(command);
    }

    /**
     * 批量删除案例
     */
    @PostMapping("/batchDelete")
    @Operation(summary = "批量删除案例")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, paramResolver = KbRepoQaAuthParamResolver.class),
        @Auth(uniqueId = "batchDeleteQA", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbQATeamManagerResourceAuthParamResolver.class)
    })
    public Boolean batchDelete(@Valid @RequestBody IdListCommand command) {
        return casesService.batchDelete(command);
    }

    /**
     * excel批量导入案例库
     */
    @PostMapping("/import")
    @Operation(summary = "excel批量导入案例库")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "importQA", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Boolean importExcel(@Valid @RequestBody KbRepoCasesUploadCommand command) {
        return casesService.importExcel(command);
    }
}

