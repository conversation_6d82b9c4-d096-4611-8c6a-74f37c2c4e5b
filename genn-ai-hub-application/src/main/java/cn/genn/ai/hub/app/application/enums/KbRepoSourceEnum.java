package cn.genn.ai.hub.app.application.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KbRepoSourceEnum {

    TEAM("team", "团队"),
    PERSONAL("personal", "个人"),
    GEWU("gewu", "格物"),
//    COMMON("common", "公共"),

    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;
}
