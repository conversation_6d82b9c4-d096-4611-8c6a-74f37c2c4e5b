package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.dto.KbRepoDataDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataAndIndexUpdateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataChunkRequestCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataQuery;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataSmartChunkSummaryCommand;
import cn.genn.ai.hub.app.application.service.action.KbRepoDataActionService;
import cn.genn.ai.hub.app.application.service.query.KbRepoDataQueryService;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.config.auth.Auth;
import cn.genn.ai.hub.app.infrastructure.config.auth.BatchOrAuth;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team.KbCollectionTeamManagerResourceAuthParamResolver;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 知识库数据集的数据条目
 * <AUTHOR>
 */
@Tag(name = "知识库数据集的数据条目")
@RestController
@RequestMapping("/kbRepoData")
@RequiredArgsConstructor
public class KbRepoDataController {

    private final KbRepoDataQueryService queryService;
    private final KbRepoDataActionService actionService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询列表数据集下数据条目")
    public PageResultDTO<KbRepoDataDTO> page(@Parameter(description = "查询类") @RequestBody KbRepoDataQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return
     */
    @PostMapping("/get")
    @Operation(summary = "根据id查询")
    public KbRepoDataDTO get(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return queryService.get(query);
    }


    /**
     * 按指定/新增的数据条目进行分词分块,可以整个数据集/某个条目/用户录入文本
     */
    @PostMapping("/chunk")
    @Operation(summary = "按指定/新增的数据条目进行分词分块,可以整个数据集/某个条目/用户录入文本,并保存")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "chunkData", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long chunk(@Valid @RequestBody KbRepoDataChunkRequestCommand command) {
        return actionService.chunk(command);
    }

    /**
     * 更新或新增数据条目的内容和索引内容
     */
    @PostMapping("/updateOrCreateData")
    @Operation(summary = "更新或新增数据条目的内容和索引内容")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "updateOrCreateData", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Long updateOrCreateData(@Valid @RequestBody KbRepoDataAndIndexUpdateCommand command) {
        return actionService.updateOrCreateData(command);
    }

    /**
     * 删除某个数据条目
     */
    @PostMapping("/deleteData")
    @Operation(summary = "删除某个数据条目")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "deleteData", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public Boolean deleteData(@RequestBody KbRepoDataAndIndexUpdateCommand command) {
        return actionService.deleteDataByDataKey(command);
    }

    /**
     * 对分块生成智能摘要
     */
    @PostMapping("/smartChunkSummaryForData")
    @Operation(summary = "对分块生成智能摘要")
    @BatchOrAuth({
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.repoId"),
        @Auth(uniqueId = "smartChunkSummaryForData", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbCollectionTeamManagerResourceAuthParamResolver.class)
    })
    public String smartChunkSummaryForData(@Valid @RequestBody KbRepoDataSmartChunkSummaryCommand command) {
        return actionService.smartChunkSummaryForData(command);
    }
}

