package cn.genn.ai.hub.app.infrastructure.utils.vector.milvus;

import cn.genn.ai.hub.app.application.dto.KbRepoQaPairDTO;
import cn.genn.ai.hub.app.application.dto.embedding.QaPairEmbeddingDataDTO;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.spring.boot.starter.ai.component.AIModelManager;
import cn.hutool.core.collection.CollectionUtil;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.vector.request.DeleteReq;
import io.milvus.v2.service.vector.request.InsertReq;
import jakarta.annotation.Resource;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 问答对向量库操作
 *
 * @Date: 2025/4/15
 * @Author: kangjian
 */
@Component
public class QaPairVectorUtil {

    @Resource
    private MilvusClientV2 milvusClient;
    @Resource
    private AIModelManager aiModelManager;
    @Resource
    private GennAIHubProperties properties;

    public void handleQaPairVector(String embeddingModelKey, KbRepoQaPairDTO qaPairDTO) {
        if (Objects.isNull(qaPairDTO)) {
            return;
        }
        List<String> questions = new ArrayList<>();
        questions.add(qaPairDTO.getQuestion());
        if (CollectionUtil.isNotEmpty(qaPairDTO.getSimilarQuestions())) {
            questions.addAll(qaPairDTO.getSimilarQuestions());
        }
        DeleteReq deleteReq = DeleteReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfQaPair(embeddingModelKey))
            .filter("qa_pair_key==\"" + qaPairDTO.getQaPairKey() + "\"")
            .build();
        milvusClient.delete(deleteReq);
        for (String questionContent : questions) {
            EmbeddingModel embeddingModel = aiModelManager.getEmbeddingModel(embeddingModelKey);
            float[] embed = embeddingModel.embed(questionContent);
            List<Float> embedding = new ArrayList<>(embed.length);
            for (Float f : embed) {
                embedding.add(f);
            }
            // answer截取前varchar两千个字符
            String answer = qaPairDTO.getAnswer().substring(0, Math.min(qaPairDTO.getAnswer().length(), 300));
            QaPairEmbeddingDataDTO data = QaPairEmbeddingDataDTO.builder()
                .tenantId(qaPairDTO.getTenantId())
                .repoId(qaPairDTO.getRepoId())
                .question(questionContent)
                .answer(answer)
                .qaPairKey(qaPairDTO.getQaPairKey())
                .userId(qaPairDTO.getCreateUserId())
                .embedding(embedding).build();
            Gson gson = new Gson();
            JsonObject jsonObject = gson.toJsonTree(data).getAsJsonObject();
            milvusClient.insert(InsertReq.builder()
                .collectionName(properties.getMilvus().getCollectionNameOfQaPair(embeddingModelKey))
                .data(Collections.singletonList(jsonObject))
                .build());
        }
    }

    public void deleteQaPairVectorByQaPairKey(String vectorModelKey, String qaPairKey) {
        DeleteReq deleteReq = DeleteReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfQaPair(vectorModelKey))
            .filter("qa_pair_key==\"" + qaPairKey + "\"")
            .build();
        milvusClient.delete(deleteReq);
    }

    public void deleteQaPairVectorByQaPairKeyList(String vectorModelKey, List<String> qaPairKeyList) {
        DeleteReq deleteReq = DeleteReq.builder()
            .collectionName(properties.getMilvus().getCollectionNameOfQaPair(vectorModelKey))
            // 转换为类似 ['key1', 'key2', 'key3'] 的格式
            .filter("qa_pair_key in " + qaPairKeyList.stream()
                .map(key -> "\"" + key + "\"")
                .collect(Collectors.joining(", ", "[", "]")))
            .build();
        milvusClient.delete(deleteReq);
    }

}
