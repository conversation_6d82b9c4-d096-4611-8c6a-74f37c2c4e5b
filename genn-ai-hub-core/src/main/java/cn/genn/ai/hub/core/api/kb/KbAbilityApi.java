package cn.genn.ai.hub.core.api.kb;

import java.util.List;

/**
 * 知识库能力接口
 *
 * <AUTHOR>
 */
public interface KbAbilityApi {

    /**
     * 知识库检索
     */
    KbSearchResponse search(KbSearchRequest request);

    /**
     * 批量创建 案例
     */
    Boolean batchCreateCases(KbCreateCasesRequest request);

    /**
     * 实时解析文件返回原文信息
     *
     * @return 文件内容
     */
    String parseFile(String externalFileId, String externalFileUrl, String fileName, String contentType);

    /**
     * 获取术语
     * @param repoIds 知识库id
     */
    List<Term> listTerms(List<Long> repoIds);
}
