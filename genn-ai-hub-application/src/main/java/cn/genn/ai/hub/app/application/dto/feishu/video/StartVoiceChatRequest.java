package cn.genn.ai.hub.app.application.dto.feishu.video;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class StartVoiceChatRequest extends BaseVoiceChatRequest {

    @Schema(description = "自定义提示内容")
    private List<UserPrompt> userPrompts;

    @Schema(description = "snapshotConfig")
    private SnapshotConfig snapshotConfig;

    @Schema(description = "gennAiToken")
    private String gennAiToken;

}
