package cn.genn.ai.hub.core.api.kb;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KbSearchRequest {

    /**
     * 知识库id集合,支持传入多个知识库id
     */
    private List<Long> repoIds;

    /**
     * 检索模式，默认为语义检索
     */
    @Builder.Default
    private SearchModeEnum searchMode = SearchModeEnum.SEMANTIC;

    /**
     * 用户问题
     */
    private List<String> questions;

    /**
     * 是否重排序，对检索结果进行重新排序
     */
    @Builder.Default
    private Boolean needRerank = false;

    /**
     * 是否开启标准问答对拦截
     */
    @Builder.Default
    private Boolean needQaPairIntercept = false;

    /**
     * 是否开启案例库拦截
     */
    @Builder.Default
    private Boolean needCasesIntercept = false;

    private Integer topK;

    /**
     * 分数 0.0-1.0
     */
    private Double score;
}
