package cn.genn.ai.hub.app.infrastructure.config.voice;

import lombok.Data;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 */
@Data
public class ASRConfig {

    private String provider = "volcano";

    @NestedConfigurationProperty
    private ASRProviderParams providerParams = new ASRProviderParams();

    @NestedConfigurationProperty
    private VADConfig vdcConfig = new VADConfig();

    @NestedConfigurationProperty
    private InterruptConfig interruptConfig = new InterruptConfig();

    @NestedConfigurationProperty
    private Integer turnDetectionMode = 0;

}
