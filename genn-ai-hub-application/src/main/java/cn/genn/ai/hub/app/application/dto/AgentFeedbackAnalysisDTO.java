package cn.genn.ai.hub.app.application.dto;

import cn.genn.ai.hub.app.application.enums.FeedbackTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 智能体反馈分析对象
 * @date 2025-07-08
 */
@Data
public class AgentFeedbackAnalysisDTO {

    @Schema(description = "反馈数")
    private Long userCount;

    @Schema(description = "反馈数")
    private Long feedbackCount;

    @Schema(description = "like反馈数")
    private Long likeCount;

    @Schema(description = "dislike反馈数")
    private Long dislikeCount;

    @Schema(description = "wordMark反馈数")
    private Long wordMarkCount;

    @Schema(description = "反馈标签反馈统计")
    private Map<FeedbackTypeEnum, List<FeedbackTagStat>> feedbackTagStats;

    @Schema(description = "反馈日期统计")
    private List<FeedbackDayStat> feedbackDayStats;

    @Data
    public static class FeedbackTagStat {

        @Schema(description = "反馈标签")
        private String feedbackTag;

        @Schema(description = "反馈数量")
        private Long count;
    }

    @Data
    public static class FeedbackDayStat {

        @Schema(description = "日期")
        private LocalDate date;

        @Schema(description = "反馈数量")
        private Long count;

        @Schema(description = "like反馈数量")
        private Long likeCount;

        @Schema(description = "dislike反馈数量")
        private Long dislikeCount;

        @Schema(description = "wordMark反馈数量")
        private Long wordMarkCount;

    }
}
