package cn.genn.ai.hub.app.interfaces.fs;

import cn.genn.ai.hub.app.application.dto.feishu.video.BaseVoiceChatRequest;
import cn.genn.ai.hub.app.application.dto.feishu.video.SpeechLabRequest;
import cn.genn.ai.hub.app.application.dto.feishu.video.StartVoiceChatRequest;
import cn.genn.ai.hub.app.application.dto.feishu.video.UpdateVoiceChatRequest;
import cn.genn.ai.hub.app.application.service.video.FSVoiceService;
import cn.genn.ai.hub.app.application.service.video.PodcastsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * OpenAI 音视频
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "音视频")
@RestController
@RequestMapping("/voice")
public class FSVoiceController {

    @Resource
    private FSVoiceService videoService;
    @Resource
    private PodcastsService podcastsService;

    @PostMapping("/StartVoiceChat")
    @Operation(summary = "启动音视频聊天")
    public void StartVoiceChat(@RequestBody StartVoiceChatRequest command) {
        videoService.StartVoiceChat(command);
    }

    @PostMapping("/figure/StartVoiceChat")
    @Operation(summary = "数字人音视频聊天")
    public void figureStartVoiceChat(@RequestBody StartVoiceChatRequest command) {
        videoService.figureStartVoiceChat(command);
    }

    @PostMapping("/UpdateVoiceChat")
    @Operation(summary = "更新音视频聊天内容")
    public void UpdateVoiceChat(@RequestBody UpdateVoiceChatRequest command) {
        videoService.UpdateVoiceChat(command);
    }

    @PostMapping("/StopVoiceChat")
    @Operation(summary = "结束音视频聊天")
    public void StopVoiceChat(@RequestBody BaseVoiceChatRequest command) {
        videoService.StopVoiceChat(command);
    }

    @PostMapping("/speechLab")
    @Operation(summary = "合成音频")
    public String speechLab(@RequestBody SpeechLabRequest command) {
        return podcastsService.speechLab(command);
    }

}

