package cn.genn.ai.hub.app.application.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "格物文档添加结果")
public class GewuDocumentAddResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Schema(description = "文档ID")
    private Long documentId;
    
    @Schema(description = "文档名称")
    private String name;
    
    @Schema(description = "处理状态")
    private String status;
    
    @Schema(description = "处理消息")
    private String message;
} 