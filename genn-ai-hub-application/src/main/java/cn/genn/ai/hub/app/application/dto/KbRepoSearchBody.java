package cn.genn.ai.hub.app.application.dto;

import cn.genn.ai.hub.core.api.kb.SearchModeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 对接算法的查询请求
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoSearchBody implements Serializable {

    /**
     * 知识库id集合,支持传入多个知识库id
     */
    private List<Long> repoIdList;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 数据集id集合,代表不希望检索到的数据集
     */
    private List<Long> collectionIdList;

    /**
     * 嵌入模型名称
     */
    private String embeddingModel;

    /**
     * 重排序模型名称
     * todo 待定
     */
    private String rerankModel;

    /**
     * 检索模式，默认为语义检索
     */
    @Builder.Default
    private SearchModeEnum searchMode = SearchModeEnum.SEMANTIC;

    /**
     * 用户问题
     */
    private List<String> questions;

    /**
     * 是否重排序，对检索结果进行重新排序
     */
    @Builder.Default
    private Boolean needRerank = false;

    /**
     * 是否开启标准问答对拦截
     */
    @Builder.Default
    private Boolean needQaPairIntercept = false;

    /**
     * 是否开启案例库拦截
     */
    @Builder.Default
    private Boolean needCasesIntercept = false;

    private Integer topK;

    /**
     * 分数 0.0-1.0
     */
    private Double score;
}

