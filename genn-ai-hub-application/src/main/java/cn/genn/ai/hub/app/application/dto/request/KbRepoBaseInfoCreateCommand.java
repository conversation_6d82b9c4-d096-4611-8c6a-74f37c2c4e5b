package cn.genn.ai.hub.app.application.dto.request;

import cn.genn.ai.hub.app.application.dto.feishu.FSAppConfig;
import cn.genn.ai.hub.app.application.enums.RepoTypeEnum;
import cn.genn.ai.hub.app.application.enums.KbRepoSourceEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * KbRepoBaseInfoDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoBaseInfoCreateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "创建在哪个目录id下")
    private Long catId;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "团队id")
    private Long teamId;

    @Schema(description = "标签id")
    private List<Long> tagIds;

    @Schema(description = "知识库类型")
    private RepoTypeEnum repoType;

    @Schema(description = "知识库图标，支持自定义图标 URL")
    private String avatar;

    @Schema(description = "向量模型--对应模型管理的name")
    private String vectorModelKey;

    @Schema(description = "文本识别模型--对应模型管理的name")
    private String agentModelKey;

    @Schema(description = "图片识别模型名称--对应模型管理的name")
    private String imgModelKey;

    @Schema(description = "简介，知识库的功能描述")
    private String description;

    @Schema(description = "飞书配置")
    private FSAppConfig feishuServerConfig;

    @Schema(description = "应用渠道关联ID")
    private Long channelRefId;

    @Schema(description = "应用ID", hidden = true)
    private Long channelId;

    @Schema(description = "知识库来源类型")
    private KbRepoSourceEnum source;
}

