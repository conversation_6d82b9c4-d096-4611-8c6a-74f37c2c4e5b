package cn.genn.ai.hub.app.infrastructure.repository.persistence;

import cn.genn.ai.hub.app.application.assembler.KbRepoFileAssembler;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoCollectionMapper;
import cn.genn.ai.hub.app.infrastructure.repository.mapper.KbRepoFileMapper;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoCollectionPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoDataIndexPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoFilePO;
import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.enums.DeletedTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * @Date: 2025/3/7
 * @Author: kangjian
 */
@Repository
public class KbRepFileRepositoryImpl extends ServiceImpl<KbRepoFileMapper, KbRepoFilePO> {

    @Resource
    private KbRepoFileMapper mapper;
    @Resource
    private KbRepoFileAssembler assembler;


    public KbRepoFilePO getFileByExternalId(String externalFileId) {
        QueryWrapper<KbRepoFilePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            .eq(KbRepoFilePO::getExternalFileId, externalFileId)
            .eq(KbRepoFilePO::getDeleted, DeletedTypeEnum.NOT_DELETED);
        return mapper.selectOne(queryWrapper);
    }

    public void updateFileLengthAndUrl(Long fileId, Integer length, String preSignedUrl) {
        UpdateWrapper<KbRepoFilePO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
            .eq(KbRepoFilePO::getId, fileId)
            .set(Objects.nonNull(length), KbRepoFilePO::getLength, length)
            .set(StringUtils.isNotBlank(preSignedUrl), KbRepoFilePO::getExternalFileUrl, preSignedUrl);
        update(updateWrapper);
    }
}
