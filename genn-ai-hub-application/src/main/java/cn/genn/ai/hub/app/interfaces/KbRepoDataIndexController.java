package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.dto.KbRepoDataIndexDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoDataIndexQuery;
import cn.genn.ai.hub.app.application.service.action.KbRepoDataIndexActionService;
import cn.genn.ai.hub.app.application.service.query.KbRepoDataIndexQueryService;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识库数据条目索引
 * <AUTHOR>
 */
@Tag(name = "知识库数据条目索引")
@RestController
@RequestMapping("/kbRepoDataIndex")
@RequiredArgsConstructor
public class KbRepoDataIndexController {

    private final KbRepoDataIndexQueryService queryService;
    private final KbRepoDataIndexActionService actionService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询列表")
    public PageResultDTO<KbRepoDataIndexDTO> page(@Parameter(description = "查询类") @RequestBody KbRepoDataIndexQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return
     */
    @PostMapping("/get")
    @Operation(summary = "根据id查询")
    public KbRepoDataIndexDTO get(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return queryService.get(query);
    }

    /**
     * 根据dataKey获取索引列表
     */
    @PostMapping("/getByDataKey")
    @Operation(summary = "根据dataKey获取索引列表")
    public List<KbRepoDataIndexDTO> getByDataKey(@RequestBody KbRepoDataIndexQuery query) {
        return queryService.getByDataKey(query);
    }
}

