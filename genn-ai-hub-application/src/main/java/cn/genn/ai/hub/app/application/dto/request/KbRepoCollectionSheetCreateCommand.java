package cn.genn.ai.hub.app.application.dto.request;

import cn.genn.ai.hub.app.application.enums.CollectionTypeEnum;
import cn.genn.ai.hub.app.application.enums.TrainingModelEnum;
import cn.genn.ai.hub.app.application.enums.TrainingTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新建文本数据集 本地上传
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KbRepoCollectionSheetCreateCommand implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "父id")
    private Long pid;

    @Schema(description = "知识库ID，标识集合所属的数据库（关联 repo 表）")
    private Long repoId;

    @Schema(description = "文件id")
    @NotNull(message = "fileId不能为空")
    private Long fileId;

    @Schema(description = "表格sheetIndex")
    @NotNull(message = "sheetIndex不能为空")
    private Integer sheetIndex = 0;

    @Schema(description = "文件名")
    private String name;

    @Schema(description = "类型, folder:FOLDER-文件夹, file:FILE-文件, web:WEB-网页, manual_text:MANUAL_TEXT-手动文本")
    private CollectionTypeEnum collectionType = CollectionTypeEnum.SHEET;

    @Schema(description = "训练模式, manual:MANUAL-手动, auto:AUTO-自动")
    private TrainingTypeEnum trainingType = TrainingTypeEnum.AUTO;

    @Schema(description = "处理方式 直接分段 增强分段 问答拆分")
    private TrainingModelEnum handleType;

    @Schema(description = "表格多少行作为一个分块", example = "1")
    private Integer chunckSize = 1;

    @Schema(description = "智能分块摘要开关")
    private Boolean smartChunkSummary = false;

    @Schema(description = "租户ID", hidden = true)
    private Long tenantId;

}

