package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.dto.GewuDocumentAddResult;
import cn.genn.ai.hub.app.application.dto.KbRepoCollectionDTO;
import cn.genn.ai.hub.app.application.dto.request.GewuDocumentAddCommand;
import cn.genn.ai.hub.app.application.dto.request.GewuDocumentUrlCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionQuery;
import cn.genn.ai.hub.app.application.service.GewuDocumentService;
import cn.genn.core.model.page.PageResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/gewu")
@Tag(name = "格物知识库")
@RequiredArgsConstructor
public class GewuDocumentController {

    private final GewuDocumentService gewuDocumentService;

    @PostMapping("/documents")
    @Operation(summary = "查询文档列表")
    public PageResultDTO<KbRepoCollectionDTO> queryDocuments(@RequestBody KbRepoCollectionQuery query) {
        return gewuDocumentService.queryDocuments(query);
    }

    @PostMapping("/addGeneral")
    @Operation(summary = "添加通用文档")
    public GewuDocumentAddResult addDocument(@RequestBody GewuDocumentAddCommand command) {
        return gewuDocumentService.addDocument(command);
    }

    @PostMapping("/documents/url")
    @Operation(summary = "导入网页链接")
    public GewuDocumentAddResult importUrl(@RequestBody GewuDocumentUrlCommand command) {
        return gewuDocumentService.handleUrlImport(command.getUrl(), command.getName(), command.getDescription());
    }

}
