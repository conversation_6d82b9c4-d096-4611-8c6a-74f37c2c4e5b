package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.command.FileStorageQueryCommand;
import cn.genn.ai.hub.app.application.command.TosTempStsConfigCreateCommand;
import cn.genn.ai.hub.app.application.dto.FileStorageTempStsConfigDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoFileDTO;
import cn.genn.ai.hub.app.application.dto.request.KbRepoFileQuery;
import cn.genn.ai.hub.app.application.service.action.KbRepoFileActionService;
import cn.genn.ai.hub.app.application.service.query.KbRepoFileQueryService;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

/**
 * 知识库文件信息
 * <AUTHOR>
 */
@Tag(name = "知识库文件信息")
@RestController
@RequestMapping("/kbRepoFile")
@RequiredArgsConstructor
public class KbRepoFileController {

    private final KbRepoFileQueryService queryService;
    private final KbRepoFileActionService actionService;
    private final KbRepoFileActionService fileActionService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询列表")
    public PageResultDTO<KbRepoFileDTO> page(@Parameter(description = "查询类") @RequestBody KbRepoFileQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return
     */
    @PostMapping("/get")
    @Operation(summary = "根据id查询")
    public KbRepoFileDTO get(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return queryService.get(query);
    }

    /**
     * 根据文件路径获取外链
     */
    @Operation(summary = "根据外部文件路径获取外链")
    @PostMapping("/getFileUrlByExternalFileId")
    public String getFileUrl(@Parameter(description = "查询类") @RequestBody KbRepoFileQuery query) {
        return actionService.getFileUrl(query.getExternalFileId());
    }

    @PostMapping("/uploadLog")
    @Operation(summary = "上传文件后记录")
    public Long uploadLog(@Parameter(description = "上传文件后记录") @RequestBody KbRepoFileDTO kbRepoFileDTO) {
        return actionService.uploadLog(kbRepoFileDTO);
    }

    /**
     * 上传文件，成功返回文件信息
     */
    @Operation(summary = "上传文件")
    @PostMapping(value = "/upload")
    public KbRepoFileDTO upload(@RequestPart(value = "file") MultipartFile file,
                                @RequestParam(value = "repoId", required = false) Long repoId, @RequestParam(value = "collectionId", required = false) Long collectionId) {
        Long tenant = CurrentUserHolder.getTenantId();
        return fileActionService.uploadFile(tenant, file, Optional.ofNullable(repoId).orElse(0L), Optional.ofNullable(collectionId).orElse(0L));
    }

    /**
     *
     */
    @Operation(summary = "上传文件（未登录）")
    @PostMapping(value = "/uploadWithoutLogin")
    public KbRepoFileDTO uploadWithoutLogin(@RequestPart(value = "file") MultipartFile file) {
        return fileActionService.uploadWithoutLogin(null, file);
    }


    @Operation(summary = "获取火山云TOS的STS临时配置")
    @PostMapping("/getTosTempStsConfig")
    public FileStorageTempStsConfigDTO getTosStsConfig(@Valid @RequestBody TosTempStsConfigCreateCommand command) {
        return fileActionService.getTosStsConfig(command);
    }

    @Operation(summary = "获取华为云OBS的STS临时配置")
    @PostMapping("/getObsTempStsConfig")
    public FileStorageTempStsConfigDTO getObsTempStsConfig() {
        return fileActionService.getObsTempStsConfig();
    }

    @Operation(summary = "获取当前租户对应的存储平台类型")
    @PostMapping("/getStoragePlatformType")
    public String getStoragePlatformType(@Valid @RequestBody FileStorageQueryCommand command) {
        return fileActionService.getStoragePlatformType(command.getToken());
    }


    /**
     * 根据externalFileUrl查询
     *
     * @param query
     * @return KbRepoFileDTO
     */
    @Operation(summary = "根据externalFileUrl查询")
    @PostMapping("/getByExternalFileUrl")
    public KbRepoFileDTO getByExternalFileUrl(@Parameter(description = "查询类") @RequestBody KbRepoFileQuery query) {
        return queryService.getByExternalFileUrl(query.getExternalFileUrl());
    }
}

