package cn.genn.ai.hub.app.application.service;

import cn.genn.ai.hub.app.application.assembler.FSAssembler;
import cn.genn.ai.hub.app.application.command.FSDocPreviewCommand;
import cn.genn.ai.hub.app.application.command.FSDocRefreshCommand;
import cn.genn.ai.hub.app.application.command.FSDocSyncCommand;
import cn.genn.ai.hub.app.application.command.FSSheetSyncCommand;
import cn.genn.ai.hub.app.application.dto.*;
import cn.genn.ai.hub.app.application.dto.feishu.*;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionTextOfFileCreateCommand;
import cn.genn.ai.hub.app.application.dto.request.KbRepoCollectionTextOfTextCreateCommand;
import cn.genn.ai.hub.app.application.enums.CollectionTypeEnum;
import cn.genn.ai.hub.app.application.enums.TrainingModelEnum;
import cn.genn.ai.hub.app.application.enums.TrainingTypeEnum;
import cn.genn.ai.hub.app.application.enums.feishu.FSFileType;
import cn.genn.ai.hub.app.application.processor.AIHubInvokeService;
import cn.genn.ai.hub.app.application.query.FSSheetContentQuery;
import cn.genn.ai.hub.app.application.query.FSSpaceQuery;
import cn.genn.ai.hub.app.application.service.action.KbRepoCollectionActionService;
import cn.genn.ai.hub.app.application.service.action.KbRepoFileActionService;
import cn.genn.ai.hub.app.application.service.action.KbRepoSearchActionService;
import cn.genn.ai.hub.app.application.service.action.KbRepoTaskActionService;
import cn.genn.ai.hub.app.application.storage.FileStorageOperation;
import cn.genn.ai.hub.app.domain.channel.service.FeishuClientService;
import cn.genn.ai.hub.app.infrastructure.config.GennAIFileStorageProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennAIHubProperties;
import cn.genn.ai.hub.app.infrastructure.config.GennRequestContext;
import cn.genn.ai.hub.app.infrastructure.external.feishu.FSDocParser;
import cn.genn.ai.hub.app.infrastructure.external.feishu.FSUtil;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.ChannelBaseRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepCollectionRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.persistence.KbRepoBaseInfoRepositoryImpl;
import cn.genn.ai.hub.app.infrastructure.repository.po.ChannelBasePO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoBaseInfoPO;
import cn.genn.ai.hub.app.infrastructure.repository.po.KbRepoFilePO;
import cn.genn.ai.hub.core.api.channel.ChannelConfig;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.spring.boot.starter.upm.component.CurrentUserHolder;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.lark.oapi.Client;
import com.lark.oapi.service.docx.v1.model.ListDocumentBlockReq;
import com.lark.oapi.service.docx.v1.model.ListDocumentBlockResp;
import com.lark.oapi.service.docx.v1.model.ListDocumentBlockRespBody;
import com.lark.oapi.service.drive.v1.model.DownloadFileReq;
import com.lark.oapi.service.drive.v1.model.DownloadFileResp;
import com.lark.oapi.service.sheets.v3.model.QuerySpreadsheetSheetReq;
import com.lark.oapi.service.sheets.v3.model.QuerySpreadsheetSheetResp;
import com.lark.oapi.service.wiki.v2.model.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FSWikiFileService {
    @Resource
    private FSAssembler fsaAssembler;
    @Resource
    private FeishuClientService clientService;
    @Resource
    private KbRepoBaseInfoRepositoryImpl repoBaseInfoRepository;
    @Resource
    private KbRepoCollectionActionService collectionActionService;
    @Resource
    private AIHubInvokeService invokeService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private KbRepCollectionRepositoryImpl collectionRepository;
    @Resource
    private FileStorageOperation storageOperation;
    @Resource
    private KbRepoSearchActionService searchActionService;
    @Resource
    private ChannelBaseRepositoryImpl channelBaseRepository;
    @Resource
    private KbRepoFileActionService repoFileActionService;
    @Resource
    private GennAIHubProperties aiHubProperties;

    public void deepImport(FSDeepImport deepImport, boolean needUpdate) {
        //，深度遍历所有文件
        try {
            GennRequestContext.initTenantId(deepImport.getTenantId());
            for (NodeData nodeData : deepImport.getNodeData()) {
                // 查询下级列表
                FSSpaceQuery fsQuery = fsaAssembler.convertFSQ(deepImport, nodeData);
                List<Node> nodes = getNodes(fsQuery, Lists.newArrayList());
                List<NodeData> processNodes = new ArrayList<>();
                for (Node item : nodes) {
                    // 目前支持 file、doc、docx深度导入
                    if (FSFileType.isSupportDeepImport(item.getObjType())) {
                        FSFileType byCode = FSFileType.getByCode(item.getObjType());
                        NodeData pn = new NodeData();
                        pn.setSpaceId(item.getSpaceId());
                        pn.setNodeToken(item.getNodeToken());
                        pn.setObjToken(item.getObjToken());
                        pn.setObjType(byCode);
                        pn.setDeepImport(item.getHasChild());
                        processNodes.add(pn);
                    }
                }

                if (CollUtil.isNotEmpty(processNodes)) {
                    FSDocSyncCommand command = fsaAssembler.convertFSC(deepImport);
                    command.setNodeData(processNodes);
                    importFSDocx(command, needUpdate);
                }

            }
        } catch (Exception e) {
            log.error("深度导入飞书文档异常 ", e);
        } finally {
            GennRequestContext.clear();
        }
    }

    private List<Node> getNodes(FSSpaceQuery fsQuery, List<Node> nodes) {
        ListSpaceNodeRespBody spaceNode = getSpaceList(fsQuery);
        Node[] items = spaceNode.getItems();
        if (items == null) {
            return nodes;
        }
        nodes.addAll(Arrays.stream(items).toList());
        if (StringUtils.isBlank(fsQuery.getSpaceId())) {
            return nodes;
        }
        if (spaceNode.getHasMore()) {
            fsQuery.setPageToken(spaceNode.getPageToken());
            getNodes(fsQuery, nodes);
        }
        return nodes;
    }

    private Map<Long, String> getExistingNodes(Long repoId) {
        List<KbRepoCollectionDTO> sourceCllDTOs = collectionRepository.collIdsByRepoId(repoId);
        if (CollUtil.isEmpty(sourceCllDTOs)) {
            return new HashMap<>();
        }
        return sourceCllDTOs.stream()
            .map(coll -> {
                FSSyncParams params = JsonUtils.parse(coll.getSyncParams(), FSSyncParams.class);
                if (params != null && params.getObjToken() != null) {
                    return Map.entry(coll.getId(), params.getObjToken());
                }
                return null;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (existing, replacement) -> existing
            ));
    }

    public void importFSDocx(FSDocSyncCommand command, boolean needUpdate) {
        // <collId, objToken>
        Map<Long, String> existingNodes = getExistingNodes(command.getRepoId());

        List<NodeData> importDocs = new ArrayList<>();
        List<Long> updateCollIds = new ArrayList<>();
        List<NodeData> deepImportDocs = new ArrayList<>();

        // 分类处理
        for (NodeData doc : command.getNodeData()) {
            boolean foundMatch = false;
            if (MapUtils.isNotEmpty(existingNodes)) {
                for (Map.Entry<Long, String> entry : existingNodes.entrySet()) {
                    if (StringUtils.equals(entry.getValue(), doc.getObjToken())) {
                        if (needUpdate) {
                            updateCollIds.add(entry.getKey());
                        }
                        foundMatch = true;
                        break;
                    }
                }
            }
            if (!foundMatch) {
                importDocs.add(doc);
            }

            if (doc.getDeepImport()) {
                deepImportDocs.add(doc);
            }
        }
        // 批量执行导入
        if (CollUtil.isNotEmpty(importDocs)) {
            importDocs.forEach(doc -> {
                try {
                    doImport(command, doc);
                } catch (Exception e) {
                    log.error("执行飞书文档导入失败，文档key: {}", doc.getObjToken(), e);
                }
            });
        }

        // 批量刷新更新项
        if (CollUtil.isNotEmpty(updateCollIds)) {
            FSDocRefreshCommand refreshCommand = new FSDocRefreshCommand();
            refreshCommand.setRepoId(command.getRepoId());
            refreshCommand.setAppId(command.getAppId());
            refreshCommand.setCollectionIds(Lists.newArrayList(updateCollIds));
            Thread.startVirtualThread(() ->
                {
                    try {
                        GennRequestContext.initTenantId(command.getTenantId());
                        refresh(refreshCommand);
                    } catch (Exception e) {
                        log.error("深度导入飞书文档异常 ", e);
                    } finally {
                        GennRequestContext.clear();
                    }
                }
            );
        }

        // 深度导入处理
        if (CollUtil.isNotEmpty(deepImportDocs)) {
            FSDeepImport deepImport = fsaAssembler.convertDI(command);
            deepImport.setNodeData(deepImportDocs);
            Thread.startVirtualThread(() -> deepImport(deepImport, needUpdate));
        }
    }

    private void doImport(FSDocSyncCommand command, NodeData doc) {
        Client client = clientService.getClient(command.getAppId());
        if (FSFileType.isFile(doc.getObjType())) {
            DownloadFileResp fsFile = getFSFile(client, doc.getObjToken());
            String objectKey = "/fs/docx/" + doc.getObjToken() + "/" + fsFile.getFileName();
            if (storageOperation.saveOutPutStream(fsFile.getData(), objectKey, command.getTenantId())) {
                Long fileId = processFSFile(command.getRepoId(), objectKey, fsFile);
                FSSyncParams syncParams = new FSSyncParams();
                syncParams.setObjToken(doc.getObjToken());
                KbRepoCollectionTextOfFileCreateCommand createCommand = KbRepoCollectionTextOfFileCreateCommand
                    .builder()
                    .repoId(command.getRepoId())
                    .fileId(fileId)
                    .name(fsFile.getFileName())
                    .collectionType(CollectionTypeEnum.FS_DOCX)
                    .handleType(TrainingModelEnum.CHUNK)
                    .chunkSize(command.getChunkSize())
                    .chunkSplitter(command.getChunkSplitter())
                    .deepParse(command.getDeepParse())
                    .smartChunkSummary(command.getSmartChunkSummary())
                    .syncParams(JsonUtils.toJson(syncParams))
                    .externalFileType(FSFileType.FILE.getCode())
                    .deepImport(doc.getDeepImport())
                    .tenantId(command.getTenantId())
                    .build();
                collectionActionService.createTextCollectionOfFile(createCommand);
            }
        }
        if (FSFileType.isDocx(doc.getObjType())) {
            ListDocumentBlockRespBody fsDocResult = getFsDocResult(doc.getObjToken(), client);
            FSDocParser parser = new FSDocParser(command.getRepoId(), command.getAppId(), doc.getObjToken(), client);
            FSAnalysisResult markdown = parser.doParser(fsDocResult);
            if (StringUtils.isBlank(markdown.getMarkdown())) {
                return;
            }
            // 地址拼接
            String objectKey = "/fs/docx/" + doc.getObjToken() + "/" + markdown.getName();
            storageOperation.saveContentTOFile(markdown.getMarkdown(), objectKey);
            String preSignedUrl = storageOperation.preSignedUrlGetObject(objectKey, null);
            createCollection(command, markdown, objectKey, preSignedUrl, doc.getDeepImport());
        }
    }


    private Long processFSFile(Long repoId, String objectKey, DownloadFileResp fsFile) {
        ByteArrayOutputStream fileData = fsFile.getData();
        byte[] byteArray = fileData.toByteArray();
        String preSignedUrl = storageOperation.preSignedUrlGetObject(objectKey, null);

        KbRepoFilePO file = repoFileActionService.getFile(objectKey);
        if (file != null) {
            repoFileActionService.updateFileLengthAndUrl(file.getId(), byteArray.length, preSignedUrl);
            return file.getId();
        }

        String suffix = StringUtils.substringAfterLast(fsFile.getFileName(), ".");
        String contentType = getFileContentType(suffix);
        GennAIFileStorageProperties tos = aiHubProperties.getTos();
        return repoFileActionService.uploadLog(KbRepoFileDTO.builder()
            .repoId(repoId)
            .filePlatform(tos.getBucketName())
            .externalFileId(objectKey)
            .externalFileUrl(preSignedUrl)
            .fileName(fsFile.getFileName())
            .contentType(contentType)
            .length(byteArray.length)
            .build());
    }

    private static String getFileContentType(String suffix) {
        if (StringUtils.equals(suffix, "docx") || StringUtils.equals(suffix, "doc")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        }
        if (StringUtils.equals(suffix, "ppt")) {
            return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
        }
        if (StringUtils.equals(suffix, "pdf")) {
            return "application/pdf";
        }
        if (StringUtils.equals(suffix, "txt")) {
            return "text/plain";
        }
        return "text/markdown";
    }

    private ListDocumentBlockRespBody getFsDocResult(String doc, Client client) {
        ListDocumentBlockReq req = ListDocumentBlockReq.newBuilder()
            .documentId(doc)
            .documentRevisionId(-1)
            .build();
        // 发起请求
        ListDocumentBlockResp resp = null;
        try {
            resp = client.docx().v1().documentBlock().list(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            return new ListDocumentBlockRespBody();
        }
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            return new ListDocumentBlockRespBody();
        }
        return resp.getData();

    }

    private void createCollection(FSDocSyncCommand command, FSAnalysisResult markdown, String objectKey, String externalFileUrl, Boolean deepImport) {
        FSSyncParams syncParams = new FSSyncParams();
        syncParams.setObjToken(markdown.getDocumentId());
        KbRepoCollectionTextOfTextCreateCommand createCollCommand = KbRepoCollectionTextOfTextCreateCommand
            .builder()
            .repoId(command.getRepoId())
            .name(markdown.getName())
            .rawText(markdown.getMarkdown())
            .collectionType(CollectionTypeEnum.FS_DOCX)
            .trainingType(TrainingTypeEnum.AUTO)
            .handleType(TrainingModelEnum.CHUNK)
            .chunkSize(command.getChunkSize())
            .chunkSplitter(command.getChunkSplitter())
            .deepParse(command.getDeepParse())
            .smartChunkSummary(command.getSmartChunkSummary())
            .externalFileId(objectKey)
            .externalFileUrl(externalFileUrl)
            .syncParams(JsonUtils.toJson(syncParams))
            .externalFileType(FSFileType.DOCX.getCode())
            .deepImport(deepImport)
            .tenantId(command.getTenantId())
            .build();
        collectionActionService.createTextCollectionOfText(createCollCommand);
    }

    public ListSpaceNodeRespBody getSpaceList(FSSpaceQuery query) {
        Client client = clientService.getClient(query.getAppId());
        if (StringUtils.isEmpty(query.getSpaceId())) {
            Node spaceNode = getSpaceNode(query.getFolderToken(), client);
            ListSpaceNodeRespBody respBody = new ListSpaceNodeRespBody();
            respBody.setItems(new Node[]{spaceNode});
            return respBody;
        }
        List<FSFileType> fileTypes = Lists.newArrayList(query.getObjType());
        if (FSFileType.isDocx(query.getObjType())) {
            fileTypes.add(FSFileType.FILE);
            fileTypes.add(FSFileType.DOC);
        }
        return getSpaceList(query, fileTypes, client);

    }

    private ListSpaceNodeRespBody getSpaceList(FSSpaceQuery query, List<FSFileType> fileTypes, Client client) {

        ListSpaceNodeReq req = ListSpaceNodeReq.newBuilder()
            .spaceId(query.getSpaceId())
            .pageSize(query.getPageSize())
            .parentNodeToken(query.getNodeToken())
            .build();
        if (StringUtils.isNotBlank(query.getPageToken())) {
            req.setPageToken(query.getPageToken());
        }

        // 发起请求
        ListSpaceNodeResp resp = null;
        try {
            resp = client.wiki().v2().spaceNode().list(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            throw new BusinessException("调用飞书接口异常: " + e.getMessage());
        }
        // 处理服务端错误
        assert resp != null;
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException(resp.getError().getMessage());
        }
        ListSpaceNodeRespBody spaceList = resp.getData();
        if (spaceList.getItems() == null) {
            // 如果是空节点需要设置一个 空数组
            spaceList.setItems(new Node[0]);
        }
        if (CollUtil.isNotEmpty(fileTypes)) {
            spaceList.setItems(Arrays.stream(spaceList.getItems())
                .filter(item -> fileTypes.contains(FSFileType.getByCode(item.getObjType())))
                .toArray(Node[]::new));
        }
        return spaceList;
    }

    private Node getSpaceNode(String folderToken, Client client) {
        GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder()
            .token(folderToken)
            .build();

        // 发起请求
        GetNodeSpaceResp resp = null;
        try {
            resp = client.wiki().v2().space().getNode(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }

        // 处理服务端错误
        assert resp != null;
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException(resp.getError().getMessage());
        }
        return resp.getData().getNode();
    }

    public SheetListBody getSheetList(String appId, String objToken) {
        Client client = clientService.getClient(appId);
        QuerySpreadsheetSheetReq req = QuerySpreadsheetSheetReq
            .newBuilder()
            .spreadsheetToken(objToken)
            .build();

        // 发起请求
        QuerySpreadsheetSheetResp resp = null;
        try {
            resp = client.sheets().v3().spreadsheetSheet().query(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常: {}", e.getMessage());
            throw new BusinessException(e.getMessage());
        }

        // 处理服务端错误
        if (!resp.success()) {
            log.error("调用飞书接口返回值出错: {}", resp.getError().getMessage());
            throw new BusinessException(resp.getError().getMessage());
        }

        // 业务数据处理
        return SheetListBody.builder()
            .objToken(objToken)
            .sheets(resp.getData().getSheets())
            .build();
    }

    public SheetDataResp getSheetContent(FSSheetContentQuery query) {
        KbRepoBaseInfoPO repo = repoBaseInfoRepository.getRepoById(query.getRepoId());

        ChannelBasePO channelBasePO = channelBaseRepository.getById(repo.getChannelId());
        ChannelConfig parsedConfig = channelBasePO.getParsedConfig();

        String tenantAccessToken = getFSAccessToken(parsedConfig.getAppId(), parsedConfig.getAppSecret());
        // 构建请求实体
        String ranges = query.getRanges();
        if (StringUtils.isBlank(ranges)) {
            ranges = "A:Z";
        }
        String option = query.getValueRenderOption();
        if (StringUtils.isBlank(option)) {
            option = "ToString";
        }
        return getSheetData(query.getObjToken(), query.getSheetId(), ranges, option, tenantAccessToken);
    }

    private SheetDataResp getSheetData(String objToken, String sheetId, String ranges, String option, String tenantAccessToken) {

        String sourceUrl = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/values/%s?valueRenderOption=%s&dateTimeRenderOption=FormattedString";
        String url = String.format(sourceUrl, objToken, sheetId + "!" + ranges, option);
        SheetCellRespBody sheetCellRespBody = invokeService.fetchFSGet(url, tenantAccessToken);
        return sheetCellRespBody.getData();
    }

    private String getFSAccessToken(String appId, String appSecret) {
        FSTokenRequest request = new FSTokenRequest();
        request.setApp_id(appId);
        request.setApp_secret(appSecret);

        try {
            String url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            HttpEntity<String> httpEntity = new HttpEntity<>(JsonUtils.toJson(request), headers);
            ResponseEntity<FSAppTokenResp> responseEntity = restTemplate.postForEntity(url, httpEntity, FSAppTokenResp.class);
            log.info("invoke, result status code: {}", responseEntity.getStatusCode());
            if (HttpStatus.OK != responseEntity.getStatusCode()) {
                log.error("invoke, failed with status code: {}", responseEntity.getStatusCode());
                throw new BusinessException("Failed to invoke FS_API-tenant_access_token with status code: " + responseEntity.getStatusCode());
            }
            log.info("invoke, result: {}", responseEntity.getBody());
            FSAppTokenResp body = responseEntity.getBody();
            return body.getTenantAccessToken();
        } catch (Exception e) {
            log.error("调用飞书接口返回值出错: ", e);
        }
        return StringUtils.EMPTY;
    }

    public void asyncSheet(FSSheetSyncCommand command) {

        SheetDataResp sheetContent = getSheetContent(fsaAssembler.convert(command));
        WikiSheetCell valueRange = sheetContent.getValueRange();
        // 表格内容
        List<List<Object>> values = valueRange.getValues();
        if (values == null || values.isEmpty()) {
            log.warn("No data found in sheet content.");
            throw new BusinessException("未获取到飞书表格内容，请检查源表格");
        }
        String sourceSheetContent = FSUtil.convertToMarkdownTable(values);
        String objectKey = "/fs/sheet/" + command.getObjToken() + "/" + command.getTitle() + ".md";
        storageOperation.saveContentTOFile(sourceSheetContent, objectKey);

        List<Map<Integer, String>> cachedDataList = convertStructure(values);
        // 分块存储 索引、向量化
        collectionActionService.createFSCollection(command, cachedDataList, objectKey);

    }

    private List<Map<Integer, String>> convertStructure(List<List<Object>> values) {
        List<Map<Integer, String>> result = new ArrayList<>();
        // 遍历每一行数据
        for (List<Object> row : values) {
            Map<Integer, String> rowMap = new HashMap<>();
            // 遍历每一列数据
            for (int i = 0; i < row.size(); i++) {
                String cur = StringUtils.EMPTY;
                if (row.get(i) != null) {
                    cur = row.get(i).toString();
                }
                rowMap.put(i, cur);
            }
            result.add(rowMap);
        }
        return result;
    }

    @Transactional
    public Boolean refresh(FSDocRefreshCommand command) {
        List<KbRepoCollectionDTO> sourceCllDTOs = collectionRepository.selectCollectByRepoId(command.getRepoId(), command.getCollectionIds());
        for (KbRepoCollectionDTO sourceCllDTO : sourceCllDTOs) {
            try {
                if (StringUtils.equals(sourceCllDTO.getExternalFileType(), "file")) {
                    refreshFile(command.getAppId(), command.getRepoId(), sourceCllDTO);
                }

                if (StringUtils.equals(sourceCllDTO.getExternalFileType(), "docx")) {
                    refreshDocx(command.getAppId(), command.getRepoId(), sourceCllDTO);
                }
                if (StringUtils.equals(sourceCllDTO.getExternalFileType(), "sheet")) {
                    refreshSheet(sourceCllDTO);
                }
            } catch (Exception e) {
                log.error("更新飞书文档失败 数据集ID {}", sourceCllDTO.getId(), e);
            }
        }
        return true;
    }

    public void refreshFile(String appId, Long repoId, KbRepoCollectionDTO sourceCllDTO) {
        Client client = clientService.getClient(appId);
        FSSyncParams params = JsonUtils.parse(sourceCllDTO.getSyncParams(), FSSyncParams.class);
        if (params == null) {
            params = new FSSyncParams();
        }
        DownloadFileResp fsFile = getFSFile(client, params.getObjToken());
        String objectKey = "/fs/docx/" + params.getObjToken() + "/" + fsFile.getFileName();
        if (storageOperation.saveOutPutStream(fsFile.getData(), objectKey, sourceCllDTO.getTenantId())) {
            Long fileId = processFSFile(repoId, objectKey, fsFile);
            FSSyncParams syncParams = new FSSyncParams();
            syncParams.setObjToken(params.getObjToken());
            KbRepoAfreshTrainCommand createCommand = KbRepoAfreshTrainCommand
                .builder()
                .repoId(sourceCllDTO.getRepoId())
                .collectionId(sourceCllDTO.getId())
                .fileId(fileId)
                .name(fsFile.getFileName())
                .collectionType(CollectionTypeEnum.FS_DOCX)
                .handleType(TrainingModelEnum.CHUNK)
                .chunkSize(sourceCllDTO.getChunkSize())
                .chunkSplitter(sourceCllDTO.getChunkSplitter())
                .deepParse(sourceCllDTO.getDeepParse())
                .smartChunkSummary(sourceCllDTO.getSmartChunkSummary())
                .syncParams(JsonUtils.toJson(syncParams))
                .externalFileType(FSFileType.FILE.getCode())
                .tenantId(sourceCllDTO.getTenantId())
                .build();
            collectionActionService.afreshTrain(createCommand);
        }
    }

    public void refreshDocx(String appId, Long repoId, KbRepoCollectionDTO sourceCllDTO) {
        Client client = clientService.getClient(appId);
        FSSyncParams params = JsonUtils.parse(sourceCllDTO.getSyncParams(), FSSyncParams.class);
        if (params == null) {
            params = new FSSyncParams();
        }
        ListDocumentBlockRespBody fsDocResult = getFsDocResult(params.getObjToken(), client);
        FSDocParser parser = new FSDocParser(repoId, appId, params.getObjToken(), client);
        FSAnalysisResult result = parser.doParser(fsDocResult);
        String markdown = result.getMarkdown();
        if (StringUtils.isBlank(markdown)) {
            throw new BusinessException("未获取到飞书文档内容，请检查源文档");
        }
        // 地址拼接
        String objectKey = "/fs/docx/" + params.getObjToken() + "/" + result.getName();
        storageOperation.saveContentTOFile(markdown, objectKey);
        String preSignedUrl = storageOperation.preSignedUrlGetObject(objectKey, result.getName());

        sourceCllDTO.setExternalFileId(objectKey);
        sourceCllDTO.setExternalFileUrl(preSignedUrl);
        sourceCllDTO.setRawTextLength(StrUtil.length(markdown));
        sourceCllDTO.setHashRawText(MD5.create().digestHex(markdown));
        collectionRepository.updateByDTO(sourceCllDTO);

        collectionActionService.deleteCollAndDataAndIndex(sourceCllDTO.getId(), sourceCllDTO.getRepoId(), sourceCllDTO.getTenantId());
        // 通知算法分块
        KbRepoTaskOfTextChunkBody taskBody = KbRepoTaskOfTextChunkBody.builder()
            .tenantId(sourceCllDTO.getTenantId())
            .repoId(repoId)
            .collectionId(sourceCllDTO.getId())
            .contentType("text/plain")
            .rawText(markdown)
            .build();
        SpringUtil.getBean(KbRepoTaskActionService.class).createTaskOfTextChunk(taskBody);
    }


    public void refreshSheet(KbRepoCollectionDTO sourceCllDTO) {
        FSSyncParams params = JsonUtils.parse(sourceCllDTO.getSyncParams(), FSSyncParams.class);
        if (params == null) {
            params = new FSSyncParams();
        }
        FSSheetContentQuery sheetContentQuery = FSSheetContentQuery.builder()
            .repoId(sourceCllDTO.getRepoId())
            .objToken(params.getObjToken())
            .sheetId(sourceCllDTO.getExternalSheetId())
            .ranges(params.getRanges())
            .valueRenderOption(params.getValueRenderOption())
            .build();
        SheetDataResp sheetContent = getSheetContent(sheetContentQuery);
        WikiSheetCell valueRange = sheetContent.getValueRange();
        // 表格内容
        List<List<Object>> values = valueRange.getValues();
        if (values == null || values.isEmpty()) {
            log.warn("No data found in sheet content.");
            throw new BusinessException("未获取到飞书表格内容，请检查源表格");
        }
        List<Map<Integer, String>> cachedDataList = convertStructure(values);
        // 分块存储 索引、向量化
        collectionActionService.updateFSSheetCollection(sourceCllDTO, cachedDataList);
    }

    public List<KbRepoRealtimeFileParseBody> docxPreview(FSDocPreviewCommand command) {
        Client client = clientService.getClient(command.getAppId());
        NodeData nodeData = command.getNodeData();
        String objectKey = "/fs/docx/" + nodeData.getObjToken() + "/";
        if (FSFileType.isFile(nodeData.getObjType())) {
            DownloadFileResp fsFile = getFSFile(client, nodeData.getObjToken());
            if (storageOperation.saveOutPutStream(fsFile.getData(), objectKey + fsFile.getFileName(), CurrentUserHolder.getTenantId())) {
                // 生成一个外链
                String preSignedUrl = storageOperation.preSignedUrlGetObject(objectKey, null);
                return searchActionService.realtimeFileParseChunk(objectKey, preSignedUrl, fsFile.getFileName(), "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            }
        }
        if (FSFileType.isDocx(nodeData.getObjType())) {
            ListDocumentBlockRespBody fsDocResult = getFsDocResult(nodeData.getObjToken(), client);
            FSDocParser parser = new FSDocParser(command.getRepoId(), command.getAppId(), nodeData.getObjToken(), client);
            FSAnalysisResult markdown = parser.doParser(fsDocResult);
            if (StringUtils.isBlank(markdown.getMarkdown())) {
                return Lists.newArrayList();
            }
            if (storageOperation.saveContentTOFile(markdown.getMarkdown(), objectKey + markdown.getName())) {
                // 生成一个外链
                String preSignedUrl = storageOperation.preSignedUrlGetObject(objectKey, null);
                return searchActionService.realtimeFileParseChunk(objectKey, preSignedUrl, markdown.getName(), "text/markdown");
            }
        }

        return Lists.newArrayList();
    }


    public static DownloadFileResp getFSFile(Client client, String fileToken) {
        // 创建请求对象
        DownloadFileReq req = DownloadFileReq.newBuilder()
            .fileToken(fileToken)
            .build();

        // 发起请求
        DownloadFileResp resp = null;
        try {
            resp = client.drive().v1().file().download(req);
        } catch (Exception e) {
            log.error("调用飞书接口异常： {}", e.getMessage());
            throw new BusinessException("调用飞书接口异常: " + e.getMessage());
        }
        return resp;
    }
}

