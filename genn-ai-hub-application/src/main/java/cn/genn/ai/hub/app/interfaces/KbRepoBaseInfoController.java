package cn.genn.ai.hub.app.interfaces;

import cn.genn.ai.hub.app.application.command.ResourceInviteCommand;
import cn.genn.ai.hub.app.application.command.ResourceRemoveInviteCommand;
import cn.genn.ai.hub.app.application.command.TagsAssignCommand;
import cn.genn.ai.hub.app.application.dto.CollaboratorDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoBaseInfoDTO;
import cn.genn.ai.hub.app.application.dto.KbRepoCatDetailDTO;
import cn.genn.ai.hub.app.application.dto.request.*;
import cn.genn.ai.hub.app.application.service.action.KbRepoBaseInfoActionService;
import cn.genn.ai.hub.app.application.service.action.KbRepoCategoryActionService;
import cn.genn.ai.hub.app.application.service.query.KbRepoBaseInfoQueryService;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ActionType;
import cn.genn.ai.hub.app.domain.auth.model.valobj.ResourceType;
import cn.genn.ai.hub.app.infrastructure.config.auth.*;
import cn.genn.ai.hub.app.infrastructure.utils.auth.complex.parse.team.KbTeamManagerResourceAuthParamResolver;
import cn.genn.core.model.ddd.IdCommand;
import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识库基础信息
 * <AUTHOR>
 */
@Tag(name = "知识库基础信息")
@RestController
@RequestMapping("/kbRepoBaseInfo")
@RequiredArgsConstructor
public class KbRepoBaseInfoController {

    private final KbRepoBaseInfoQueryService queryService;
    private final KbRepoBaseInfoActionService actionService;
    private final KbRepoCategoryActionService catActionService;


    /**
     * 分页查询列表
     *
     * @param query 查询条件
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询列表")
    @BatchOrAuthAction({
        @AuthAction(resourceType = ResourceType.KB_REPO),
        @AuthAction(resourceType = ResourceType.TEAM, resourceField = "teamId")
    })
    @BatchOrAuthExpression({
        @AuthExpression(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.VIEW, ActionType.EDIT}),
        @AuthExpression(resourceType = ResourceType.TEAM, actionType = {ActionType.VIEW, ActionType.MANAGER}, dbFieldName = "team_id")
    })
    public PageResultDTO<KbRepoBaseInfoDTO> page(@Parameter(description = "查询类") @RequestBody KbRepoBaseInfoQuery query) {
        return queryService.page(query);
    }

    /**
     * 根据id查询
     *
     * @param query
     * @return
     */
    @PostMapping("/get")
    @Operation(summary = "根据id查询")
    @BatchOrAuthAction({
        @AuthAction(resourceType = ResourceType.KB_REPO),
        @AuthAction(resourceType = ResourceType.TEAM, resourceField = "teamId")
    })
    @BatchOrAuthExpression({
        @AuthExpression(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.VIEW, ActionType.EDIT}),
        @AuthExpression(resourceType = ResourceType.TEAM, actionType = {ActionType.VIEW, ActionType.MANAGER}, dbFieldName = "team_id")
    })
    public KbRepoBaseInfoDTO get(@Parameter(description = "查询类") @RequestBody IdQuery query) {
        return queryService.get(query);
    }

    /**
     * 获取知识库列表的所有目录结构
     */
    @PostMapping("getKbRepoCatDetail")
    @Operation(summary = "获取知识库列表的所有目录结构")
    @Deprecated
    public List<KbRepoCatDetailDTO> getKbRepoCatDetail(@RequestBody KbRepoCatDetailQuery query) {
        return queryService.getKbRepoCatDetail(query);
    }

    /**
     * 新建文件夹
     */
    @PostMapping("createCat")
    @Operation(summary = "新建文件夹")
    @Deprecated
    public Long createCat(@RequestBody KbRepoCatCreateCommand command) {
        return catActionService.createCat(command);
    }


    /**
     * 新建知识库
     */
    @PostMapping("createKbRepo")
    @Operation(summary = "新建知识库")
    @Auth(resourceType = ResourceType.TEAM, actionType = {ActionType.VIEW, ActionType.MANAGER}, fieldKey = "#command.teamId", allowFieldKeyNull = true)
    public Long createKbRepo(@RequestBody KbRepoBaseInfoCreateCommand command) {
        return actionService.createKbRepo(command);
    }

    /**
     * 移动数据库的目录 知识库id和目录id会重复
     */
    @PostMapping("moveKbRepoCat")
    @Operation(summary = "移动数据库的目录")
    @Deprecated
    public Boolean moveKbRepoCat(@RequestBody KbRepoCatMoveCommand command) {
        return catActionService.moveKbRepoCat(command);
    }

    /**
     * 编辑文件夹介绍
     */
    @PostMapping("editCatDetail")
    @Operation(summary = "编辑文件夹信息")
    @Deprecated
    public Boolean editCatDetail(@RequestBody KbRepoCatDetailEditCommand command) {
        return catActionService.editCatDetail(command);
    }

    /**
     * 编辑知识库介绍
     */
    @PostMapping("editKbRepoDetail")
    @Operation(summary = "编辑知识库信息")
    @BatchOrAuth(
        value = {
            @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.EDIT, ActionType.MANAGER}, fieldKey = "#command.id"),
            @Auth(uniqueId = "editKbRepoDetail", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbTeamManagerResourceAuthParamResolver.class)
        }
    )
    public Boolean editKbRepoDetail(@RequestBody KbRepoBaseInfoEditCommand command) {
        return actionService.editKbRepoDetail(command);
    }

    /**
     * 删除文件夹 存在文件，目录 不让删
     */
    @PostMapping("deleteCat")
    @Operation(summary = "删除文件夹")
    @Deprecated
    public Boolean deleteCat(@RequestBody IdQuery command) {
        return catActionService.deleteCat(command);
    }

    /**
     * 删除知识库
     */
    @PostMapping("deleteKbRepo")
    @Operation(summary = "删除知识库")
    @BatchOrAuth(
        value = {
            @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER}, fieldKey = "#command.id"),
            @Auth(uniqueId = "deleteKbRepo", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbTeamManagerResourceAuthParamResolver.class)
        }
    )
    public Boolean deleteKbRepo(@RequestBody IdCommand command) {
        return actionService.deleteKbRepo(command);
    }

    /**
     * 分配标签
     */
    @PostMapping("/assignTags")
    @Operation(summary = "分配标签")
    @BatchOrAuth(
        value = {
            @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER, ActionType.EDIT}, fieldKey = "#command.id"),
            @Auth(uniqueId = "assignTags", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbTeamManagerResourceAuthParamResolver.class)
        }
    )
    public void assignTags(@RequestBody @Valid TagsAssignCommand command) {
        actionService.assignTags(command);
    }

    /**
     * 邀请协作
     */
    @PostMapping("/inviteCollaborator")
    @Operation(summary = "邀请协作")
    @BatchOrAuth(value = {
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER}, fieldKey = "#command.id"),
        @Auth(uniqueId = "inviteCollaborator", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbTeamManagerResourceAuthParamResolver.class)
    })
    public Boolean inviteCollaborator(@RequestBody @Valid ResourceInviteCommand command) {
        return actionService.inviteCollaborator(command);
    }

    /**
     * 移除协作者
     */
    @PostMapping("/removeCollaborator")
    @Operation(summary = "移除协作者")
    @BatchOrAuth(value = {
        @Auth(resourceType = ResourceType.KB_REPO, actionType = {ActionType.MANAGER}, fieldKey = "#command.id"),
        @Auth(uniqueId = "removeCollaborator", resourceType = ResourceType.TEAM, actionType = {ActionType.MANAGER}, paramResolver = KbTeamManagerResourceAuthParamResolver.class)
    })
    public Boolean removeCollaborator(@RequestBody @Valid ResourceRemoveInviteCommand command) {
        return actionService.removeCollaborator(command);
    }

    /**
     * 列出智能体所有协作者
     */
    @PostMapping("/listCollaborator")
    @Operation(summary = "列出当前知识库的协助者")
    public List<CollaboratorDTO> listCollaborator(@RequestBody @Valid IdQuery query) {
        return queryService.listCollaborator(query);
    }
}

